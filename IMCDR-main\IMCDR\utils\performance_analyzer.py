"""
性能分析工具 - 用于分析CDRIB训练过程中的性能瓶颈
"""

import time
import torch
import psutil
import numpy as np
from collections import defaultdict


class PerformanceAnalyzer:
    def __init__(self):
        self.timers = defaultdict(list)
        self.memory_usage = []
        self.gpu_usage = []
        self.start_times = {}
        
    def start_timer(self, name):
        """开始计时"""
        self.start_times[name] = time.time()
        
    def end_timer(self, name):
        """结束计时"""
        if name in self.start_times:
            elapsed = time.time() - self.start_times[name]
            self.timers[name].append(elapsed)
            del self.start_times[name]
            return elapsed
        return 0
    
    def record_memory_usage(self):
        """记录内存使用情况"""
        # CPU内存
        cpu_memory = psutil.virtual_memory()
        cpu_usage = {
            'total': cpu_memory.total / (1024**3),  # GB
            'used': cpu_memory.used / (1024**3),   # GB
            'percent': cpu_memory.percent
        }
        
        # GPU内存
        gpu_usage = {}
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_stats()
            allocated = gpu_memory.get('allocated_bytes.all.current', 0) / (1024**3)
            reserved = gpu_memory.get('reserved_bytes.all.current', 0) / (1024**3)
            gpu_usage = {
                'allocated': allocated,
                'reserved': reserved,
                'max_allocated': torch.cuda.max_memory_allocated() / (1024**3)
            }
        
        self.memory_usage.append({
            'timestamp': time.time(),
            'cpu': cpu_usage,
            'gpu': gpu_usage
        })
    
    def get_statistics(self):
        """获取统计信息"""
        stats = {}
        
        for name, times in self.timers.items():
            if times:
                stats[name] = {
                    'count': len(times),
                    'total': sum(times),
                    'avg': np.mean(times),
                    'std': np.std(times),
                    'min': min(times),
                    'max': max(times),
                    'median': np.median(times)
                }
        
        return stats
    
    def print_analysis(self, top_n=10):
        """打印性能分析结果"""
        stats = self.get_statistics()
        
        print("\n" + "="*60)
        print("性能分析报告")
        print("="*60)
        
        # 按总时间排序
        sorted_stats = sorted(stats.items(), key=lambda x: x[1]['total'], reverse=True)
        
        print(f"\n{'操作名称':<20} {'总时间(s)':<10} {'平均时间(ms)':<12} {'调用次数':<8} {'占比(%)':<8}")
        print("-" * 70)
        
        total_time = sum(stat['total'] for stat in stats.values())
        
        for i, (name, stat) in enumerate(sorted_stats[:top_n]):
            percentage = (stat['total'] / total_time) * 100 if total_time > 0 else 0
            print(f"{name:<20} {stat['total']:<10.2f} {stat['avg']*1000:<12.1f} {stat['count']:<8} {percentage:<8.1f}")
        
        # 内存使用分析
        if self.memory_usage:
            print(f"\n{'内存使用分析'}")
            print("-" * 30)
            
            latest_memory = self.memory_usage[-1]
            print(f"CPU内存使用: {latest_memory['cpu']['used']:.1f}GB / {latest_memory['cpu']['total']:.1f}GB ({latest_memory['cpu']['percent']:.1f}%)")
            
            if latest_memory['gpu']:
                print(f"GPU内存使用: {latest_memory['gpu']['allocated']:.1f}GB (已分配)")
                print(f"GPU内存保留: {latest_memory['gpu']['reserved']:.1f}GB (已保留)")
                print(f"GPU最大使用: {latest_memory['gpu']['max_allocated']:.1f}GB")
    
    def identify_bottlenecks(self):
        """识别性能瓶颈"""
        stats = self.get_statistics()
        bottlenecks = []
        
        total_time = sum(stat['total'] for stat in stats.values())
        
        for name, stat in stats.items():
            percentage = (stat['total'] / total_time) * 100 if total_time > 0 else 0
            
            # 识别瓶颈的条件
            if percentage > 20:  # 占用超过20%的时间
                bottlenecks.append({
                    'name': name,
                    'percentage': percentage,
                    'avg_time': stat['avg'],
                    'reason': '时间占比过高'
                })
            elif stat['avg'] > 0.1:  # 平均时间超过100ms
                bottlenecks.append({
                    'name': name,
                    'percentage': percentage,
                    'avg_time': stat['avg'],
                    'reason': '单次执行时间过长'
                })
            elif stat['std'] / stat['avg'] > 0.5:  # 时间波动较大
                bottlenecks.append({
                    'name': name,
                    'percentage': percentage,
                    'avg_time': stat['avg'],
                    'reason': '执行时间不稳定'
                })
        
        return bottlenecks
    
    def suggest_optimizations(self):
        """提供优化建议"""
        bottlenecks = self.identify_bottlenecks()
        suggestions = []
        
        for bottleneck in bottlenecks:
            name = bottleneck['name']
            
            if 'forward' in name.lower():
                suggestions.append(f"前向传播瓶颈 ({name}): 考虑减少模型复杂度或使用混合精度训练")
            elif 'backward' in name.lower():
                suggestions.append(f"反向传播瓶颈 ({name}): 考虑梯度累积或减少批次大小")
            elif 'contrastive' in name.lower():
                suggestions.append(f"对比学习瓶颈 ({name}): 考虑减少对比学习批次大小或负样本数量")
            elif 'fusion' in name.lower():
                suggestions.append(f"融合计算瓶颈 ({name}): 考虑简化融合网络结构")
            elif 'data' in name.lower() or 'load' in name.lower():
                suggestions.append(f"数据加载瓶颈 ({name}): 考虑增加数据加载线程或预处理数据")
            else:
                suggestions.append(f"性能瓶颈 ({name}): 需要进一步分析具体原因")
        
        return suggestions
    
    def reset(self):
        """重置所有统计信息"""
        self.timers.clear()
        self.memory_usage.clear()
        self.gpu_usage.clear()
        self.start_times.clear()


# 全局性能分析器实例
performance_analyzer = PerformanceAnalyzer()


def analyze_function(func_name):
    """装饰器：自动分析函数执行时间"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            performance_analyzer.start_timer(func_name)
            result = func(*args, **kwargs)
            performance_analyzer.end_timer(func_name)
            return result
        return wrapper
    return decorator


def print_performance_summary():
    """打印性能总结"""
    performance_analyzer.print_analysis()
    
    bottlenecks = performance_analyzer.identify_bottlenecks()
    if bottlenecks:
        print(f"\n{'发现的性能瓶颈'}")
        print("-" * 30)
        for bottleneck in bottlenecks:
            print(f"- {bottleneck['name']}: {bottleneck['reason']} (占比: {bottleneck['percentage']:.1f}%)")
    
    suggestions = performance_analyzer.suggest_optimizations()
    if suggestions:
        print(f"\n{'优化建议'}")
        print("-" * 30)
        for i, suggestion in enumerate(suggestions, 1):
            print(f"{i}. {suggestion}")


def reset_performance_stats():
    """重置性能统计"""
    performance_analyzer.reset()
