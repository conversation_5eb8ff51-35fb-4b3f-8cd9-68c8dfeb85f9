import numpy as np
import pickle
import os
import time


def load_pretrained_embeddings(opt):
    """
    加载预训练的Node2Vec和Word2Vec嵌入
    
    参数:
    - opt: 配置字典，包含dataset, feature_dim, source_item_num, target_item_num等
    
    返回:
    - node2vec_embeddings: Node2Vec嵌入 [total_items, embedding_dim]
    - word2vec_embeddings: Word2Vec嵌入 [total_items, embedding_dim]
    """
    total_items = opt["source_item_num"] + opt["target_item_num"]
    embedding_dim = opt["feature_dim"]
    dataset_name = opt.get("dataset", "default")
    embedding_dir = opt.get("embedding_dir", "./embeddings")
    
    # 构建文件路径
    node2vec_path = os.path.join(embedding_dir, f"{dataset_name}_merged_kg_node2vec_d{embedding_dim}.pkl")
    word2vec_path = os.path.join(embedding_dir, f"{dataset_name}_merged_kg_word2vec_d{embedding_dim}.pkl")
    
    # 加载嵌入
    node2vec_embeddings = _load_embedding_file(node2vec_path, total_items, embedding_dim, "Node2Vec")
    word2vec_embeddings = _load_embedding_file(word2vec_path, total_items, embedding_dim, "Word2Vec")
    
    return node2vec_embeddings, word2vec_embeddings


def _load_embedding_file(file_path, total_items, embedding_dim, embedding_type):
    """加载单个嵌入文件"""
    if not os.path.exists(file_path):
        print(f"{embedding_type}嵌入文件不存在，生成随机嵌入")
        return np.random.normal(0, 0.1, (total_items, embedding_dim))
    
    try:
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        
        # 构建嵌入矩阵
        embeddings = np.random.normal(0, 0.1, (total_items, embedding_dim))
        source_embeddings = data.get('source_embeddings', {})
        target_embeddings = data.get('target_embeddings', {})
        
        # 源域物品数量
        source_item_num = max(source_embeddings.keys()) + 1 if source_embeddings else 0
        
        # 填入源域嵌入
        for item_id, embedding in source_embeddings.items():
            if item_id < total_items:
                embeddings[item_id] = embedding[:embedding_dim]
        
        # 填入目标域嵌入
        for item_id, embedding in target_embeddings.items():
            global_id = item_id + source_item_num
            if global_id < total_items:
                embeddings[global_id] = embedding[:embedding_dim]
        
        print(f"加载{embedding_type}嵌入: {len(source_embeddings)}源域 + {len(target_embeddings)}目标域")
        return embeddings
        
    except Exception as e:
        print(f"加载{embedding_type}嵌入失败: {e}，生成随机嵌入")
        return np.random.normal(0, 0.1, (total_items, embedding_dim))


def save_embeddings(source_embeddings_dict, target_embeddings_dict, save_path, embedding_dim):
    """
    保存嵌入到文件
    
    参数:
    - source_embeddings_dict: 源域嵌入字典 {item_id: embedding}
    - target_embeddings_dict: 目标域嵌入字典 {item_id: embedding}
    - save_path: 保存文件路径
    - embedding_dim: 嵌入维度
    """
    data = {
        'source_embeddings': source_embeddings_dict,
        'target_embeddings': target_embeddings_dict,
        'dimensions': embedding_dim,
        'timestamp': time.time()
    }
    
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    with open(save_path, 'wb') as f:
        pickle.dump(data, f)
    print(f"嵌入已保存到: {save_path}")
