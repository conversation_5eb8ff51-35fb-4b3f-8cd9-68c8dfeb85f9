import pandas as pd
import os

def read_item_info(file_path):
    data = []
    with open(file_path, encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split(',', 2)  # 只分割前两个逗号
            if len(parts) == 3:
                data.append(parts)
    return pd.DataFrame(data, columns=['item_id', 'relation', 'entity'])

def filter_existing_items(df, item2id_mapping):
    """
    过滤只存在于item2id映射中的物品

    参数:
    - df: KG数据框
    - item2id_mapping: 物品ID映射字典

    返回:
    - 过滤后的数据框
    """
    # 获取存在的物品ID集合
    existing_items = set(item2id_mapping.keys())

    # 过滤前的数量
    original_count = len(df)
    original_items = df['item_id'].nunique()

    # 只保留存在映射的物品
    df_filtered = df[df['item_id'].isin(existing_items)].copy()

    # 过滤后的数量
    filtered_count = len(df_filtered)
    filtered_items = df_filtered['item_id'].nunique()

    print(f"  - 过滤前: {original_count} 条三元组, {original_items} 个物品")
    print(f"  - 过滤后: {filtered_count} 条三元组, {filtered_items} 个物品")
    print(f"  - 移除了: {original_count - filtered_count} 条三元组 ({(original_count - filtered_count)/original_count*100:.2f}%)")

    return df_filtered

def process_entity(row, relations):
    rel_id = row['relation']
    rel = relations[rel_id]
    entity = row['entity']
    if rel == 'runtime':
        try:
            val = float(entity)
            if val < 60:
                return 'short'
            elif val < 120:
                return 'medium'
            else:
                return 'long'
        except:
            return 'unknown'
    elif rel == 'releaseDate':
        import re
        year_match = re.search(r'(\d{4})', entity)
        if year_match:
            year = int(year_match.group(1))
            decade = (year // 10) * 10
            return f'{decade}s'
        else:
            return 'unknown'
    elif rel == 'numberOfPages':
        try:
            val = float(entity)
            if val < 100:
                return 'short'
            elif val < 300:
                return 'medium'
            else:
                return 'long'
        except:
            return 'unknown'
    else:
        return entity

def map_and_save(domain_name, df, item2id_path, out_kg_path, out_relation2id_path, entity2id):
    """
    映射并保存KG数据

    参数:
    - domain_name: 域名称
    - df: KG数据框
    - item2id_path: 物品ID映射文件路径
    - out_kg_path: 输出KG文件路径
    - out_relation2id_path: 输出关系映射文件路径
    - entity2id: 实体ID映射字典
    """
    print(f"\n处理 {domain_name} 域:")

    # 读取item_id映射
    item_map = pd.read_csv(item2id_path)
    item2id = dict(zip(item_map['raw_item_id'], item_map['mapped_item_id']))
    print(f"  - 加载了 {len(item2id)} 个物品映射")

    # 过滤只存在于映射中的物品
    df = filter_existing_items(df, item2id)

    if len(df) == 0:
        print(f"  - 警告: {domain_name} 域没有有效的KG三元组!")
        # 创建空文件
        pd.DataFrame(columns=['item_id', 'relation', 'entity']).to_csv(out_kg_path, index=False)
        pd.DataFrame(columns=['relation', f'relation_id_{domain_name}']).to_csv(out_relation2id_path, index=False)
        return

    # 应用item_id映射
    df['item_id'] = df['item_id'].map(item2id)

    # 检查映射后是否有NaN值
    nan_count = df['item_id'].isna().sum()
    if nan_count > 0:
        print(f"  - 警告: {nan_count} 个物品ID映射失败，将被移除")
        df = df.dropna(subset=['item_id'])

    # 将item_id转换为整数类型
    df['item_id'] = df['item_id'].astype(int)

    # relation独立映射
    relations = sorted(df['relation'].unique())
    relation2id = {rel: i for i, rel in enumerate(relations)}
    df['relation'] = df['relation'].map(relation2id)
    print(f"  - 映射了 {len(relations)} 种关系类型")

    # entity属性处理
    df['entity'] = df.apply(lambda row: process_entity(row, relations), axis=1)

    # entity统一映射
    unmapped_entities = set(df['entity']) - set(entity2id.keys())
    if unmapped_entities:
        print(f"  - 警告: {len(unmapped_entities)} 个实体未在全局映射中找到")
        # 过滤掉未映射的实体
        df = df[df['entity'].isin(entity2id.keys())]

    df['entity'] = df['entity'].map(entity2id)

    # 最终检查
    final_count = len(df)
    final_items = df['item_id'].nunique()
    print(f"  - 最终输出: {final_count} 条三元组, {final_items} 个物品")

    # 保存
    df.to_csv(out_kg_path, index=False)
    pd.DataFrame(list(relation2id.items()), columns=['relation', f'relation_id_{domain_name}']).to_csv(out_relation2id_path, index=False)
    print(f"  - 保存到: {out_kg_path}")

def main():
    """
    主处理函数：处理跨域知识图谱数据
    """
    print("=== 开始处理跨域知识图谱数据 ===")

    # 路径配置
    movie_kg_path = './raw_data/Book/kg_list.txt'
    music_kg_path = './raw_data/Music/kg_list.txt'
    movie_item2id_path = './Book_Music/item2id_domain1.csv'
    music_item2id_path = './Book_Music/item2id_domain2.csv'
    out_movie_kg = './Book_Music/Book_kg.txt'
    out_music_kg = './Book_Music/Music_kg.txt'
    out_movie_relation2id = './Book_Music/relation2id_domain1.csv'
    out_music_relation2id = './Book_Music/relation2id_domain2.csv'
    out_entity2id = './Book_Music/entity2id.csv'

    print(f"输入文件:")
    print(f"  - Movie KG: {movie_kg_path}")
    print(f"  - Music KG: {music_kg_path}")
    print(f"输出文件:")
    print(f"  - Movie KG: {out_movie_kg}")
    print(f"  - Music KG: {out_music_kg}")
    print(f"  - Entity映射: {out_entity2id}")

    # 读取两个域的kg三元组
    print("\n=== 第一阶段：读取和清洗数据 ===")
    movie_df = read_item_info(movie_kg_path)
    movie_df = movie_df[movie_df['entity'].notnull() & (movie_df['entity'].str.strip() != '')]
    print(f"Movie域: 读取 {len(movie_df)} 条三元组")

    music_df = read_item_info(music_kg_path)
    music_df = music_df[music_df['entity'].notnull() & (music_df['entity'].str.strip() != '')]
    print(f"Music域: 读取 {len(music_df)} 条三元组")

    # 读取物品映射以便过滤
    print("\n=== 第二阶段：加载物品映射 ===")
    movie_item_map = pd.read_csv(movie_item2id_path)
    music_item_map = pd.read_csv(music_item2id_path)
    movie_item2id = dict(zip(movie_item_map['raw_item_id'], movie_item_map['mapped_item_id']))
    music_item2id = dict(zip(music_item_map['raw_item_id'], music_item_map['mapped_item_id']))
    print(f"Movie域: {len(movie_item2id)} 个物品映射")
    print(f"Music域: {len(music_item2id)} 个物品映射")

    # 过滤存在的物品
    print("\n=== 第三阶段：过滤存在的物品 ===")
    movie_df = filter_existing_items(movie_df, movie_item2id)
    music_df = filter_existing_items(music_df, music_item2id)

    if len(movie_df) == 0 and len(music_df) == 0:
        print("错误: 没有有效的KG数据!")
        return

    # 处理entity属性（用于统一映射）
    print("\n=== 第四阶段：处理实体属性 ===")
    movie_relations = sorted(movie_df['relation'].unique())
    music_relations = sorted(music_df['relation'].unique())

    # 临时映射用于属性处理
    movie_df_temp = movie_df.copy()
    music_df_temp = music_df.copy()
    movie_df_temp['relation'] = movie_df_temp['relation'].map({rel: i for i, rel in enumerate(movie_relations)})
    music_df_temp['relation'] = music_df_temp['relation'].map({rel: i for i, rel in enumerate(music_relations)})
    movie_df_temp['entity'] = movie_df_temp.apply(lambda row: process_entity(row, movie_relations), axis=1)
    music_df_temp['entity'] = music_df_temp.apply(lambda row: process_entity(row, music_relations), axis=1)

    # 统一entity映射
    print("\n=== 第五阶段：创建统一实体映射 ===")
    all_entities = sorted(set(movie_df_temp['entity']) | set(music_df_temp['entity']))
    entity2id = {e: i for i, e in enumerate(all_entities)}
    print(f"创建了 {len(entity2id)} 个实体的统一映射")

    # 保存实体映射
    pd.DataFrame(list(entity2id.items()), columns=['entity', 'entity_id']).to_csv(out_entity2id, index=False)
    print(f"实体映射保存到: {out_entity2id}")

    # 最终映射并保存
    print("\n=== 第六阶段：最终映射和保存 ===")
    map_and_save('Movie', movie_df, movie_item2id_path, out_movie_kg, out_movie_relation2id, entity2id)
    map_and_save('Music', music_df, music_item2id_path, out_music_kg, out_music_relation2id, entity2id)

    print("\n=== 处理完成 ===")
    print("所有文件已生成完毕!")

if __name__ == '__main__':
    main()