import pandas as pd

def analyze_overlap(domain1_path, domain2_path):
    df1 = pd.read_csv(domain1_path)
    df2 = pd.read_csv(domain2_path)

    users1 = set(df1['user_id'])
    users2 = set(df2['user_id'])
    items1 = set(df1['item_id'])
    items2 = set(df2['item_id'])

    overlap_users = users1 & users2
    all_users = users1 | users2

    print(f'域1用户数: {len(users1)}')
    print(f'域2用户数: {len(users2)}')
    print(f'总用户数: {len(all_users)}')
    print(f'重叠用户数: {len(overlap_users)}')
    print(f'域1重叠用户占比: {len(overlap_users) / len(users1):.4f}')
    print(f'域2重叠用户占比: {len(overlap_users) / len(users2):.4f}')
    print(f'域1物品数: {len(items1)}')
    print(f'域2物品数: {len(items2)}')
    print(f'域1交互数: {len(df1)}')
    print(f'域2交互数: {len(df2)}')
    print(f'总交互数: {len(df1) + len(df2)}')

if __name__ == '__main__':
    # 修改为你的原始数据路径
    domain1_path = 'raw_data/Music/rating.csv'
    domain2_path = 'raw_data/Movie/rating.csv'
    analyze_overlap(domain1_path, domain2_path) 