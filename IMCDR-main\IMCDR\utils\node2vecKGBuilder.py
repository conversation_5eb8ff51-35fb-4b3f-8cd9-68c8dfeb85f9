import numpy as np
import torch
import torch.nn.functional as F
import networkx as nx
import pandas as pd
from node2vec import Node2Vec
from tqdm import tqdm
import scipy.sparse as sp
from collections import defaultdict
from sklearn.metrics.pairwise import cosine_similarity
import os
import pickle
import time


class node2vecKGBuilder:
    """
    跨域知识图谱合并器，用于将两个不同域的知识图谱通过共有的尾实体名称合并成一个跨域知识图谱，
    并应用node2vec生成嵌入
    """

    def __init__(self, opt):
        """初始化跨域知识图谱合并器"""
        self.opt = opt
        self.device = torch.device('cuda') if opt.get('cuda', False) else torch.device('cpu')
        
        # 从opt获取用户-物品交互中的物品数量
        self.source_item_num = opt.get("source_item_num", 0)
        self.target_item_num = opt.get("target_item_num", 0)

        # 初始化知识图谱相关属性
        self.source_kg_triplets = None
        self.target_kg_triplets = None
        self.merged_kg_graph = None
        
        # 初始化实体字典
        self.source_entity_dict = {}  # 源域实体名称到ID的映射
        self.target_entity_dict = {}  # 目标域实体名称到ID的映射
        self.common_entities = set()  # 真正的共有实体名称集合
        self.source_actual_entities = set()  # 源域实际使用的实体名称集合
        self.target_actual_entities = set()  # 目标域实际使用的实体名称集合
        
        # 初始化嵌入相关属性
        self.source_embeddings = None
        self.target_embeddings = None
        self.similar_item_pairs = None
        self.cross_domain_item_graph = None
        
        # 嵌入维度
        self.dimensions = opt.get("feature_dim", 128)
        
        # 嵌入文件保存路径
        self.embedding_dir = opt.get("embedding_dir", "./embeddings")
        if not os.path.exists(self.embedding_dir):
            os.makedirs(self.embedding_dir)
            
        # 数据集名称，用于生成嵌入文件名
        self.dataset_name = opt.get("dataset", "default")
        
        print(f"初始化CrossDomainKGMerger - 源域物品数: {self.source_item_num}, 目标域物品数: {self.target_item_num}")

    def load_kg_data(self, source_kg_path, target_kg_path):
        """
        加载源域和目标域的知识图谱数据

        参数:
        - source_kg_path: 源域知识图谱文件路径
        - target_kg_path: 目标域知识图谱文件路径

        返回:
        - source_kg_triplets: 源域知识图谱三元组
        - target_kg_triplets: 目标域知识图谱三元组
        """
        print(f"加载源域知识图谱: {source_kg_path}")
        source_kg_triplets = []
        try:
            import pandas as pd
            df = pd.read_csv(source_kg_path)
            for _, row in df.iterrows():
                h, r, t = int(row['item_id']), int(row['relation']), int(row['entity'])
                source_kg_triplets.append([h, r, t])
        except Exception as e:
            print(f"加载源域知识图谱出错: {e}")
            return None, None

        print(f"加载目标域知识图谱: {target_kg_path}")
        target_kg_triplets = []
        try:
            import pandas as pd
            df = pd.read_csv(target_kg_path)
            for _, row in df.iterrows():
                h, r, t = int(row['item_id']), int(row['relation']), int(row['entity'])
                target_kg_triplets.append([h, r, t])
        except Exception as e:
            print(f"加载目标域知识图谱出错: {e}")
            return None, None
        
        self.source_kg_triplets = np.array(source_kg_triplets)
        self.target_kg_triplets = np.array(target_kg_triplets)
        
        print(f"加载了 {len(source_kg_triplets)} 个源域三元组和 {len(target_kg_triplets)} 个目标域三元组")
        return source_kg_triplets, target_kg_triplets
    
    def load_entity_dictionaries(self, source_entity_dict_path, target_entity_dict_path):
        """
        加载统一的实体字典，并根据各域三元组确定实际使用的实体和共有实体
        注意：源域和目标域共享同一个实体字典文件，但各域只使用其中的部分实体

        参数:
        - source_entity_dict_path: 实体字典文件路径 (entity2id.csv)
        - target_entity_dict_path: 实体字典文件路径 (entity2id.csv) - 与source相同

        返回:
        - common_entities: 真正的共有实体名称集合
        """
        print(f"加载统一实体字典: {source_entity_dict_path}")
        try:
            import pandas as pd
            df = pd.read_csv(source_entity_dict_path)
            unified_entity_dict = {}
            for _, row in df.iterrows():
                entity_name = row['entity']
                entity_id = int(row['entity_id'])
                unified_entity_dict[entity_name] = entity_id

            # 源域和目标域都使用同一个实体字典
            self.source_entity_dict = unified_entity_dict.copy()
            self.target_entity_dict = unified_entity_dict.copy()

        except Exception as e:
            print(f"加载实体字典出错: {e}")
            return set()

        # 根据三元组数据确定各域实际使用的实体
        self._identify_actual_entities_and_common()

        print(f"统一实体字典加载完成:")
        print(f"  - 总实体数: {len(unified_entity_dict)}")
        print(f"  - 源域实际使用实体数: {len(self.source_actual_entities)}")
        print(f"  - 目标域实际使用实体数: {len(self.target_actual_entities)}")
        print(f"  - 真正的共有实体数: {len(self.common_entities)}")

        return self.common_entities

    def _identify_actual_entities_and_common(self):
        """
        根据三元组数据识别各域实际使用的实体和真正的共有实体
        """
        if self.source_kg_triplets is None or self.target_kg_triplets is None:
            print("警告：请先加载知识图谱数据")
            self.source_actual_entities = set()
            self.target_actual_entities = set()
            self.common_entities = set()
            return

        # 创建实体ID到名称的反向映射
        id_to_entity = {v: k for k, v in self.source_entity_dict.items()}

        # 从源域三元组中提取实际使用的实体
        source_entity_ids = set()
        for triplet in self.source_kg_triplets:
            source_entity_ids.add(triplet[2])  # 尾实体ID

        self.source_actual_entities = set()
        for entity_id in source_entity_ids:
            if entity_id in id_to_entity:
                self.source_actual_entities.add(id_to_entity[entity_id])

        # 从目标域三元组中提取实际使用的实体
        target_entity_ids = set()
        for triplet in self.target_kg_triplets:
            target_entity_ids.add(triplet[2])  # 尾实体ID

        self.target_actual_entities = set()
        for entity_id in target_entity_ids:
            if entity_id in id_to_entity:
                self.target_actual_entities.add(id_to_entity[entity_id])

        # 计算真正的共有实体（在两个域的三元组中都实际出现的实体）
        self.common_entities = self.source_actual_entities & self.target_actual_entities

        print(f"实体使用情况分析:")
        print(f"  - 源域独有实体: {len(self.source_actual_entities - self.common_entities)}")
        print(f"  - 目标域独有实体: {len(self.target_actual_entities - self.common_entities)}")
        print(f"  - 真正共有实体: {len(self.common_entities)}")

        # 打印一些共有实体的例子（如果有的话）
        if self.common_entities:
            sample_common = list(self.common_entities)[:5]
            print(f"  - 共有实体示例: {sample_common}")
        else:
            print("  - 警告：没有发现共有实体！")
    
    def build_merged_kg_graph(self):
        """
        构建合并的知识图谱网络，正确处理共有实体和独有实体

        返回:
        - merged_kg_graph: 合并后的知识图谱网络（NetworkX图）
        """
        print("构建合并的知识图谱网络...")

        # 创建合并的知识图谱网络
        G = nx.Graph()

        # 创建实体ID到名称的反向映射
        id_to_entity = {v: k for k, v in self.source_entity_dict.items()}

        # 添加源域三元组
        print("添加源域三元组...")
        source_entity_nodes = set()
        for h, r, t in tqdm(self.source_kg_triplets):
            # 为物品节点添加域前缀
            h_node = f"s_item_{h}"

            # 如果尾实体在实体字典中，获取其名称
            if t in id_to_entity:
                entity_name = id_to_entity[t]

                # 对于共有实体，使用统一的节点名称；对于独有实体，添加域前缀
                if entity_name in self.common_entities:
                    t_node = f"entity_{entity_name}"  # 共有实体不加前缀
                else:
                    t_node = f"s_entity_{entity_name}"  # 源域独有实体加前缀

                source_entity_nodes.add(t_node)

                # 添加节点和边
                G.add_node(h_node, type="source_item", original_id=h)
                G.add_node(t_node, type="entity", original_id=t, name=entity_name,
                          is_common=(entity_name in self.common_entities))
                G.add_edge(h_node, t_node, relation=r, domain="source")

        # 添加目标域三元组
        print("添加目标域三元组...")
        target_entity_nodes = set()
        for h, r, t in tqdm(self.target_kg_triplets):
            # 为物品节点添加域前缀
            h_node = f"t_item_{h}"

            # 如果尾实体在实体字典中，获取其名称
            if t in id_to_entity:
                entity_name = id_to_entity[t]

                # 对于共有实体，使用统一的节点名称；对于独有实体，添加域前缀
                if entity_name in self.common_entities:
                    t_node = f"entity_{entity_name}"  # 共有实体不加前缀
                else:
                    t_node = f"t_entity_{entity_name}"  # 目标域独有实体加前缀

                target_entity_nodes.add(t_node)

                # 添加节点和边
                G.add_node(h_node, type="target_item", original_id=h)
                G.add_node(t_node, type="entity", original_id=t, name=entity_name,
                          is_common=(entity_name in self.common_entities))
                G.add_edge(h_node, t_node, relation=r, domain="target")

        self.merged_kg_graph = G

        # 统计图中的节点和边
        source_items = [n for n in G.nodes if G.nodes[n]['type'] == "source_item"]
        target_items = [n for n in G.nodes if G.nodes[n]['type'] == "target_item"]
        entities = [n for n in G.nodes if G.nodes[n]['type'] == "entity"]
        common_entity_nodes = [n for n in entities if G.nodes[n].get('is_common', False)]

        print(f"合并知识图谱构建完成:")
        print(f"  - 总节点数: {G.number_of_nodes()}")
        print(f"  - 总边数: {G.number_of_edges()}")
        print(f"  - 源域物品节点: {len(source_items)}")
        print(f"  - 目标域物品节点: {len(target_items)}")
        print(f"  - 实体节点总数: {len(entities)}")
        print(f"  - 共有实体节点: {len(common_entity_nodes)}")
        print(f"  - 源域独有实体节点: {len(source_entity_nodes - set(common_entity_nodes))}")
        print(f"  - 目标域独有实体节点: {len(target_entity_nodes - set(common_entity_nodes))}")

        return G
    
    def get_embedding_file_path(self, dimensions=None):
        """
        获取嵌入文件路径
        
        参数:
        - dimensions: 嵌入维度
        
        返回:
        - embedding_file_path: 嵌入文件路径
        """
        if dimensions is None:
            dimensions = self.dimensions
            
        # 使用数据集名称、嵌入维度和源域/目标域物品数量生成唯一的文件名
        file_name = f"{self.dataset_name}_merged_kg_node2vec_d{dimensions}.pkl"
        return os.path.join(self.embedding_dir, file_name)
    
    def save_embeddings(self, node_embeddings, dimensions=None):
        """
        保存节点嵌入到文件
        
        参数:
        - node_embeddings: 节点嵌入字典 {node_id: embedding_vector}
        - dimensions: 嵌入维度
        """
        embedding_file = self.get_embedding_file_path(dimensions)
        
        # 保存嵌入和相关信息
        data_to_save = {
            'node_embeddings': node_embeddings,
            'source_embeddings': self.source_embeddings,
            'target_embeddings': self.target_embeddings,
            'dimensions': dimensions if dimensions else self.dimensions,
            'timestamp': time.time()
        }
        
        try:
            with open(embedding_file, 'wb') as f:
                pickle.dump(data_to_save, f)
            print(f"节点嵌入已保存到: {embedding_file}")
            return True
        except Exception as e:
            print(f"保存节点嵌入出错: {e}")
            return False
    
    def load_embeddings(self, dimensions=None):
        """
        从文件加载节点嵌入
        
        参数:
        - dimensions: 嵌入维度
        
        返回:
        - node_embeddings: 节点嵌入字典 {node_id: embedding_vector}
        - loaded: 是否成功加载
        """
        embedding_file = self.get_embedding_file_path(dimensions)
        
        if not os.path.exists(embedding_file):
            print(f"嵌入文件不存在: {embedding_file}")
            return None, False
        
        try:
            with open(embedding_file, 'rb') as f:
                data = pickle.load(f)
                
            # 提取嵌入和相关信息
            node_embeddings = data['node_embeddings']
            self.source_embeddings = data['source_embeddings']
            self.target_embeddings = data['target_embeddings']
            
            # 检查维度是否匹配
            loaded_dimensions = data['dimensions']
            if dimensions is not None and loaded_dimensions != dimensions:
                print(f"警告: 加载的嵌入维度 ({loaded_dimensions}) 与请求的维度 ({dimensions}) 不匹配")
            
            # 显示嵌入文件的创建时间
            timestamp = data.get('timestamp', 'unknown')
            if timestamp != 'unknown':
                time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
                print(f"加载了创建于 {time_str} 的嵌入文件")
            
            print(f"成功从 {embedding_file} 加载节点嵌入")
            print(f"加载了 {len(self.source_embeddings)} 个源域物品和 {len(self.target_embeddings)} 个目标域物品的嵌入")
            
            return node_embeddings, True
        except Exception as e:
            print(f"加载节点嵌入出错: {e}")
            return None, False
    
    def generate_node2vec_embeddings(self, dimensions=None, walk_length=5, num_walks=5,
                                   p=1, q=1, workers=1, window=10, min_count=1, batch_words=4,
                                   force_regenerate=False):
        """
        使用Node2Vec生成节点嵌入，如果已存在嵌入文件则直接加载
        
        参数:
        - dimensions: 嵌入维度
        - walk_length: 随机游走长度
        - num_walks: 每个节点的随机游走次数
        - p: 返回参数
        - q: 出入参数
        - workers: 并行worker数量
        - window: Word2Vec窗口大小
        - min_count: Word2Vec最小计数
        - batch_words: Word2Vec批处理大小
        - force_regenerate: 是否强制重新生成嵌入，即使已存在嵌入文件
        
        返回:
        - node_embeddings: 节点嵌入字典 {node_id: embedding_vector}
        """
        if self.merged_kg_graph is None:
            print("错误：请先构建合并的知识图谱网络")
            return None
        
        # 如果未指定维度，使用默认值
        if dimensions is None:
            dimensions = self.dimensions
        
        # 尝试加载已有的嵌入文件
        if not force_regenerate:
            node_embeddings, loaded = self.load_embeddings(dimensions)
            if loaded:
                return node_embeddings
        
        print(f"使用Node2Vec生成节点嵌入 (维度: {dimensions})...")
        
        # 获取图的大小
        num_nodes = self.merged_kg_graph.number_of_nodes()
        print(f"图中节点数量: {num_nodes}")
        

        return self.process_large_graph(dimensions, walk_length, num_walks, p, q, workers, window, min_count, batch_words)
    
    def process_large_graph(self, dimensions=None, walk_length=30, num_walks=5, 
                          p=1, q=1, workers=1, window=10, min_count=1, batch_words=4):
        """
        分块处理大型图，避免内存问题和随机游走卡住
        
        参数:
        - dimensions: 嵌入维度
        - walk_length: 随机游走长度（对大图使用较小的值）
        - num_walks: 每个节点的随机游走次数（对大图使用较小的值）
        - p, q, workers, window, min_count, batch_words: 同generate_node2vec_embeddings
        
        返回:
        - node_embeddings: 节点嵌入字典 {node_id: embedding_vector}
        """
        if dimensions is None:
            dimensions = self.dimensions
            
        print(f"使用分块处理方法生成大型图的嵌入 (维度: {dimensions}, 游走长度: {walk_length}, 游走次数: {num_walks})...")
        
        # 获取所有节点
        nodes = list(self.merged_kg_graph.nodes())
        num_nodes = len(nodes)
        
        # 确定分块大小
        chunk_size = min(10000, num_nodes // 5)  # 每块最多10000个节点，或总节点数的1/5
        if chunk_size < 1000:
            chunk_size = 1000  # 确保每块至少有1000个节点
            
        print(f"将图分为大约 {(num_nodes + chunk_size - 1) // chunk_size} 个块，每块约 {chunk_size} 个节点")
        
        # 初始化节点嵌入字典
        all_embeddings = {}
        
        # 分块处理
        import random
        random.shuffle(nodes)  # 随机打乱节点，使每块包含不同类型的节点
        
        for i in range(0, num_nodes, chunk_size):
            chunk_nodes = nodes[i:min(i+chunk_size, num_nodes)]
            print(f"处理块 {i//chunk_size + 1}/{(num_nodes+chunk_size-1)//chunk_size}: {len(chunk_nodes)} 个节点")
            
            # 创建子图
            subgraph = self.merged_kg_graph.subgraph(chunk_nodes)
            print(f"子图创建完成: {subgraph.number_of_nodes()} 节点, {subgraph.number_of_edges()} 边")
            
            # 创建临时文件夹
            temp_dir = f'./temp_node2vec_{i}'
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
                
            try:
                # 使用较小的参数值处理子图
                node2vec = Node2Vec(
                    subgraph, 
                    dimensions=dimensions,
                    walk_length=walk_length,  # 使用较小的游走长度
                    num_walks=num_walks,      # 使用较少的游走次数
                    p=p,
                    q=q,
                    workers=1,  # 每个子图使用单线程，避免资源竞争
                    temp_folder=temp_dir
                )
                
                # 训练模型
                print(f"训练子图 {i//chunk_size + 1} 的Node2Vec模型...")
                model = node2vec.fit(
                    window=window,
                    min_count=min_count,
                    batch_words=batch_words,
                    epochs=20  # 减少训练轮数
                )
                
                # 提取嵌入
                for node in subgraph.nodes():
                    try:
                        all_embeddings[node] = model.wv[node]
                    except KeyError:
                        continue
                        
                print(f"子图 {i//chunk_size + 1} 处理完成，当前已获取 {len(all_embeddings)} 个节点的嵌入")
                
            except Exception as e:
                print(f"处理子图 {i//chunk_size + 1} 时出错: {e}")
                print("继续处理下一个子图...")
                continue
                
            # 清理临时文件夹
            import shutil
            try:
                shutil.rmtree(temp_dir)
            except:
                pass
        
        print(f"所有子图处理完成，总共获取了 {len(all_embeddings)} 个节点的嵌入")
        
        # 分离源域和目标域物品的嵌入
        self.extract_item_embeddings(all_embeddings)
        
        # 保存嵌入到文件
        self.save_embeddings(all_embeddings, dimensions)
        
        return all_embeddings
    
    def extract_item_embeddings(self, node_embeddings):
        """
        从节点嵌入中提取源域和目标域物品的嵌入
        
        参数:
        - node_embeddings: 节点嵌入字典 {node_id: embedding_vector}
        
        返回:
        - source_embeddings: 源域物品嵌入字典 {item_id: embedding_vector}
        - target_embeddings: 目标域物品嵌入字典 {item_id: embedding_vector}
        """
        source_embeddings = {}
        target_embeddings = {}
        
        for node, embedding in node_embeddings.items():
            if node.startswith("s_item_"):
                # 提取源域物品ID
                item_id = int(node.split("_")[2])
                source_embeddings[item_id] = embedding
            elif node.startswith("t_item_"):
                # 提取目标域物品ID
                item_id = int(node.split("_")[2])
                target_embeddings[item_id] = embedding
        
        self.source_embeddings = source_embeddings
        self.target_embeddings = target_embeddings
        
        print(f"提取了 {len(source_embeddings)} 个源域物品和 {len(target_embeddings)} 个目标域物品的嵌入")
        
        return source_embeddings, target_embeddings

    def gpu_cosine_similarity(self, embeddings1, embeddings2, batch_size=1000):
        """
        GPU加速的余弦相似度计算

        参数:
        - embeddings1: 第一组嵌入 (numpy array)
        - embeddings2: 第二组嵌入 (numpy array)
        - batch_size: 批处理大小，避免GPU内存溢出

        返回:
        - similarity_matrix: 相似度矩阵 (numpy array)
        """
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 转换为GPU tensor
        emb1 = torch.tensor(embeddings1, dtype=torch.float32).to(device)
        emb2 = torch.tensor(embeddings2, dtype=torch.float32).to(device)

        # L2归一化
        emb1_norm = F.normalize(emb1, p=2, dim=1)
        emb2_norm = F.normalize(emb2, p=2, dim=1)

        n1, n2 = emb1_norm.size(0), emb2_norm.size(0)

        # 分批计算，避免内存溢出
        similarity_matrix = torch.zeros(n1, n2, device=device)

        for i in range(0, n1, batch_size):
            end_i = min(i + batch_size, n1)
            batch_emb1 = emb1_norm[i:end_i]

            for j in range(0, n2, batch_size):
                end_j = min(j + batch_size, n2)
                batch_emb2 = emb2_norm[j:end_j]

                # 计算批次相似度
                batch_sim = torch.mm(batch_emb1, batch_emb2.t())
                similarity_matrix[i:end_i, j:end_j] = batch_sim

        return similarity_matrix.cpu().numpy()

    def calculate_item_similarity(self, threshold=0.5, top_k=None):
        """
        计算所有物品之间的相似度，统一处理不区分跨域和域内

        参数:
        - threshold: 相似度阈值
        - top_k: 每个物品保留的最相似物品数量，None表示不限制

        返回:
        - similar_item_pairs: 所有相似物品对列表 [(item1_global_id, item2_global_id, similarity), ...]
        """
        if self.source_embeddings is None or self.target_embeddings is None:
            print("错误：请先生成节点嵌入")
            return []

        print("计算所有物品相似度（统一处理）...")

        # 合并所有物品嵌入，构建全局物品矩阵
        all_item_ids = []
        all_embeddings = []

        # 获取源域物品数量
        source_item_num = max(self.source_embeddings.keys()) + 1 if self.source_embeddings else 0

        # 添加源域物品（全局ID = 原ID）
        for item_id in sorted(self.source_embeddings.keys()):
            all_item_ids.append(item_id)  # 源域物品保持原ID
            all_embeddings.append(self.source_embeddings[item_id])

        # 添加目标域物品（全局ID = 原ID + source_item_num）
        for item_id in sorted(self.target_embeddings.keys()):
            global_id = item_id + source_item_num  # 目标域物品加偏移
            all_item_ids.append(global_id)
            all_embeddings.append(self.target_embeddings[item_id])

        all_embeddings = np.array(all_embeddings)
        total_items = len(all_item_ids)

        print(f"总物品数: {total_items} (源域: {len(self.source_embeddings)}, 目标域: {len(self.target_embeddings)})")

        # 计算全局相似度矩阵
        print("计算全局物品相似度矩阵...")
        start_time = time.time()

        gpu_batch_size = self.opt.get("gpu_batch_size", 1000)
        if torch.cuda.is_available():
            print(f"使用GPU加速计算相似度 (批处理大小: {gpu_batch_size})...")
            similarity_matrix = self.gpu_cosine_similarity(all_embeddings, all_embeddings, gpu_batch_size)
        else:
            print("使用CPU计算相似度...")
            similarity_matrix = cosine_similarity(all_embeddings, all_embeddings)

        print(f"相似度计算耗时: {time.time() - start_time:.2f}秒")

        # 提取相似物品对
        print("提取相似物品对...")
        start_filter_time = time.time()

        if top_k is not None:
            # Top-k过滤：为每个物品保留最相似的k个物品
            print(f"应用Top-{top_k}过滤...")

            # 根据数据规模选择最优算法
            if total_items > 5000:
                print("使用稀疏矩阵优化算法...")
                similar_pairs = self._apply_topk_filter_sparse(
                    similarity_matrix, all_item_ids, threshold, top_k
                )
            else:
                print("使用向量化算法...")
                similar_pairs = self._apply_topk_filter_unified(
                    similarity_matrix, all_item_ids, threshold, top_k
                )
        else:
            # 传统阈值过滤
            similar_pairs = self._apply_threshold_filter_unified(
                similarity_matrix, all_item_ids, threshold
            )

        print(f"筛选耗时: {time.time() - start_filter_time:.2f}秒")
        print(f"找到 {len(similar_pairs)} 对相似物品 (相似度 > {threshold})")

        # 保存结果
        self.similar_item_pairs = similar_pairs

        return similar_pairs

    def _apply_threshold_filter_unified(self, similarity_matrix, all_item_ids, threshold):
        """
        统一的阈值过滤方法

        参数:
        - similarity_matrix: 全局相似度矩阵 [all_items, all_items]
        - all_item_ids: 所有物品的全局ID列表
        - threshold: 相似度阈值

        返回:
        - similar_pairs: 过滤后的相似物品对列表
        """
        similar_pairs = []
        n_items = len(all_item_ids)

        # 创建上三角掩码，避免重复和自相似
        upper_tri_mask = np.triu(np.ones((n_items, n_items)), k=1).astype(bool)

        # 结合阈值条件和上三角掩码
        valid_mask = (similarity_matrix >= threshold) & upper_tri_mask

        # 使用向量化操作找到所有满足条件的位置
        similar_indices = np.where(valid_mask)

        if len(similar_indices[0]) > 0:
            all_item_ids_array = np.array(all_item_ids)
            similar_pairs = [
                (all_item_ids_array[i], all_item_ids_array[j], similarity_matrix[i, j])
                for i, j in zip(similar_indices[0], similar_indices[1])
            ]

        return similar_pairs

    def _apply_topk_filter_unified(self, similarity_matrix, all_item_ids, threshold, top_k):
        """
        向量化优化的Top-k过滤方法

        参数:
        - similarity_matrix: 全局相似度矩阵 [all_items, all_items]
        - all_item_ids: 所有物品的全局ID列表
        - threshold: 相似度阈值
        - top_k: 每个物品保留的最相似物品数量

        返回:
        - similar_pairs: 过滤后的相似物品对列表
        """
        print(f"开始向量化Top-{top_k}过滤...")
        start_time = time.time()

        n_items = len(all_item_ids)
        all_item_ids_array = np.array(all_item_ids)
        similar_pairs = []

        # 1. 向量化创建阈值掩码和对角线掩码
        print("创建掩码矩阵...")
        threshold_mask = similarity_matrix >= threshold  # 阈值掩码
        diag_mask = np.eye(n_items, dtype=bool)          # 对角线掩码
        valid_mask = threshold_mask & (~diag_mask)       # 有效位置掩码

        print(f"有效相似对总数: {np.sum(valid_mask)}")

        # 2. 高效批量处理Top-k选择
        print("执行向量化Top-k选择...")

        # 预分配结果列表以提高效率
        all_i_indices = []
        all_j_indices = []
        all_similarities = []

        # 分批处理以减少内存使用
        batch_size = min(1000, n_items)

        for batch_start in range(0, n_items, batch_size):
            batch_end = min(batch_start + batch_size, n_items)
            batch_indices = np.arange(batch_start, batch_end)

            # 批量获取当前批次的有效掩码
            batch_valid_mask = valid_mask[batch_start:batch_end, :]

            for local_i, global_i in enumerate(batch_indices):
                # 向量化获取当前行的有效邻居
                valid_indices = np.where(batch_valid_mask[local_i, :])[0]

                if len(valid_indices) > 0:
                    # 获取有效相似度值
                    valid_similarities = similarity_matrix[global_i, valid_indices]

                    # 优化的Top-k选择
                    if len(valid_indices) <= top_k:
                        # 如果有效邻居数 <= top_k，全部保留
                        selected_indices = valid_indices
                        selected_similarities = valid_similarities
                    else:
                        # 使用argpartition进行部分排序（比完全排序快）
                        partition_indices = np.argpartition(valid_similarities, -top_k)[-top_k:]
                        # 对top-k部分进行完全排序
                        sorted_partition = partition_indices[np.argsort(valid_similarities[partition_indices])[::-1]]
                        selected_indices = valid_indices[sorted_partition]
                        selected_similarities = valid_similarities[sorted_partition]

                    # 向量化过滤 i < j 的条件
                    mask_i_less_j = selected_indices > global_i
                    if np.any(mask_i_less_j):
                        filtered_j_indices = selected_indices[mask_i_less_j]
                        filtered_similarities = selected_similarities[mask_i_less_j]

                        # 批量添加到预分配的列表
                        n_pairs = len(filtered_j_indices)
                        all_i_indices.extend([global_i] * n_pairs)
                        all_j_indices.extend(filtered_j_indices)
                        all_similarities.extend(filtered_similarities)

            if (batch_start // batch_size + 1) % 5 == 0:
                print(f"已处理 {batch_end}/{n_items} 个物品...")

        # 3. 向量化构建最终结果
        print("构建最终结果...")
        if len(all_i_indices) > 0:
            # 转换为numpy数组进行向量化操作
            i_array = np.array(all_i_indices)
            j_array = np.array(all_j_indices)
            sim_array = np.array(all_similarities)

            # 向量化获取物品ID
            item_i_ids = all_item_ids_array[i_array]
            item_j_ids = all_item_ids_array[j_array]

            # 批量构建结果元组
            similar_pairs = list(zip(item_i_ids, item_j_ids, sim_array))

        elapsed_time = time.time() - start_time
        print(f"向量化Top-k过滤完成，耗时: {elapsed_time:.2f}秒")
        print(f"最终保留 {len(similar_pairs)} 对相似物品")

        return similar_pairs

    def _apply_topk_filter_sparse(self, similarity_matrix, all_item_ids, threshold, top_k):
        """
        使用稀疏矩阵的高效Top-k过滤方法（适用于大规模数据）

        参数:
        - similarity_matrix: 全局相似度矩阵 [all_items, all_items]
        - all_item_ids: 所有物品的全局ID列表
        - threshold: 相似度阈值
        - top_k: 每个物品保留的最相似物品数量

        返回:
        - similar_pairs: 过滤后的相似物品对列表
        """
        import scipy.sparse as sp

        print(f"开始稀疏矩阵Top-{top_k}过滤...")
        start_time = time.time()

        n_items = len(all_item_ids)
        all_item_ids_array = np.array(all_item_ids)

        # 1. 创建阈值掩码并转换为稀疏矩阵
        print("创建稀疏阈值矩阵...")
        threshold_mask = similarity_matrix >= threshold
        np.fill_diagonal(threshold_mask, False)  # 排除对角线

        # 转换为稀疏矩阵以节省内存
        sparse_sim = sp.csr_matrix(similarity_matrix * threshold_mask)
        print(f"稀疏度: {1 - sparse_sim.nnz / (n_items * n_items):.4f}")

        # 2. 高效处理每行的Top-k选择
        print("执行稀疏Top-k选择...")
        similar_pairs = []

        for i in range(n_items):
            # 获取第i行的稀疏数据
            row = sparse_sim.getrow(i)
            if row.nnz > 0:  # 如果该行有非零元素
                # 获取非零元素的列索引和值
                _, cols = row.nonzero()
                data = row.data

                if len(cols) <= top_k:
                    # 如果非零元素数 <= top_k，全部保留
                    selected_cols = cols
                    selected_similarities = data
                else:
                    # 使用argpartition选择top-k
                    top_indices = np.argpartition(data, -top_k)[-top_k:]
                    sorted_top = top_indices[np.argsort(data[top_indices])[::-1]]
                    selected_cols = cols[sorted_top]
                    selected_similarities = data[sorted_top]

                # 只添加 i < j 的对
                for j, sim in zip(selected_cols, selected_similarities):
                    if i < j:
                        similar_pairs.append((all_item_ids_array[i], all_item_ids_array[j], sim))

            if (i + 1) % 1000 == 0:
                print(f"已处理 {i + 1}/{n_items} 个物品...")

        elapsed_time = time.time() - start_time
        print(f"稀疏矩阵Top-k过滤完成，耗时: {elapsed_time:.2f}秒")
        print(f"最终保留 {len(similar_pairs)} 对相似物品")

        return similar_pairs

    def _apply_topk_filter_cross_domain(self, similarity_matrix, source_ids, target_ids, threshold, top_k):
        """
        为跨域相似度应用Top-k过滤

        参数:
        - similarity_matrix: 跨域相似度矩阵 [source_items, target_items]
        - source_ids: 源域物品ID列表
        - target_ids: 目标域物品ID列表
        - threshold: 相似度阈值
        - top_k: 每个源域物品保留的最相似目标域物品数量

        返回:
        - similar_pairs: 过滤后的相似物品对列表
        """
        similar_pairs = []
        source_ids_array = np.array(source_ids)
        target_ids_array = np.array(target_ids)

        for i, source_id in enumerate(source_ids):
            # 获取当前源域物品与所有目标域物品的相似度
            similarities = similarity_matrix[i, :]

            # 找到超过阈值的目标域物品
            valid_indices = np.where(similarities >= threshold)[0]

            if len(valid_indices) > 0:
                # 获取有效的相似度值
                valid_similarities = similarities[valid_indices]

                # 按相似度降序排序，取Top-k
                sorted_indices = np.argsort(valid_similarities)[::-1][:top_k]
                top_indices = valid_indices[sorted_indices]

                # 添加到结果列表
                for j in top_indices:
                    target_id = target_ids_array[j]
                    similarity = similarities[j]
                    similar_pairs.append((source_id, target_id, similarity))

        return similar_pairs

    def _extract_intra_domain_pairs(self, item_ids, similarity_matrix, threshold, domain_name, top_k=None):
        """
        从域内相似度矩阵中提取相似物品对

        参数:
        - item_ids: 物品ID列表
        - similarity_matrix: 相似度矩阵
        - threshold: 相似度阈值
        - domain_name: 域名称（用于日志）
        - top_k: 每个物品保留的最相似物品数量，None表示不限制

        返回:
        - similar_pairs: 相似物品对列表 [(item1_id, item2_id, similarity), ...]
        """
        print(f"筛选{domain_name}内相似物品对...")
        start_filter_time = time.time()

        if top_k is not None:
            # Top-k过滤：为每个物品保留最相似的k个物品
            print(f"应用Top-{top_k}过滤...")
            similar_pairs = self._apply_topk_filter_intra_domain(
                similarity_matrix, item_ids, threshold, top_k
            )
        else:
            # 传统阈值过滤
            # 创建上三角掩码，避免重复和自相似
            n_items = len(item_ids)
            upper_tri_mask = np.triu(np.ones((n_items, n_items)), k=1).astype(bool)

            # 结合阈值条件和上三角掩码
            valid_mask = (similarity_matrix >= threshold) & upper_tri_mask

            # 使用向量化操作找到所有满足条件的位置
            similar_indices = np.where(valid_mask)

            # 优化：使用列表推导式
            if len(similar_indices[0]) > 0:
                item_ids_array = np.array(item_ids)
                similar_pairs = [
                    (item_ids_array[i], item_ids_array[j], similarity_matrix[i, j])
                    for i, j in zip(similar_indices[0], similar_indices[1])
                ]
            else:
                similar_pairs = []

        print(f"{domain_name}内筛选耗时: {time.time() - start_filter_time:.2f}秒")
        print(f"找到 {len(similar_pairs)} 对{domain_name}内相似物品 (相似度 > {threshold})")
        return similar_pairs

    def _apply_topk_filter_intra_domain(self, similarity_matrix, item_ids, threshold, top_k):
        """
        为域内相似度应用Top-k过滤

        参数:
        - similarity_matrix: 域内相似度矩阵 [items, items]
        - item_ids: 物品ID列表
        - threshold: 相似度阈值
        - top_k: 每个物品保留的最相似物品数量

        返回:
        - similar_pairs: 过滤后的相似物品对列表
        """
        similar_pairs = []
        item_ids_array = np.array(item_ids)
        n_items = len(item_ids)

        for i in range(n_items):
            # 获取当前物品与其他物品的相似度
            similarities = similarity_matrix[i, :]

            # 排除自己，找到超过阈值的物品
            valid_indices = []
            for j in range(n_items):
                if i != j and similarities[j] >= threshold:
                    valid_indices.append(j)

            if len(valid_indices) > 0:
                # 获取有效的相似度值
                valid_similarities = similarities[valid_indices]

                # 按相似度降序排序，取Top-k
                sorted_indices = np.argsort(valid_similarities)[::-1][:top_k]
                top_indices = [valid_indices[idx] for idx in sorted_indices]

                # 添加到结果列表（避免重复，只添加i < j的对）
                for j in top_indices:
                    if i < j:  # 避免重复添加
                        item1_id = item_ids_array[i]
                        item2_id = item_ids_array[j]
                        similarity = similarities[j]
                        similar_pairs.append((item1_id, item2_id, similarity))

        return similar_pairs

    def build_unified_structural_similarity_graph(self):
        """
        构建统一的物品结构相似图，包含跨域和域内的所有相似边

        返回:
        - structural_similarity_graph: 统一的结构相似图（稀疏矩阵）
        """
        if not hasattr(self, 'similar_item_pairs') or not self.similar_item_pairs:
            print("错误：请先计算物品相似度")
            return None

        print("构建统一的物品结构相似图...")
        start_time = time.time()

        # 创建统一的物品图：源域物品 [0, source_item_num) + 目标域物品 [source_item_num, source_item_num + target_item_num)
        total_items = self.source_item_num + self.target_item_num

        # 收集所有边的信息，使用列表存储以便批量处理
        rows = []
        cols = []
        data = []

        # 收集所有相似边对，统一处理
        all_similarity_pairs = []

        # 1. 跨域相似边
        for source_id, target_id, similarity in self.similar_item_pairs:
            if source_id < self.source_item_num and target_id < self.target_item_num:
                # 目标域物品索引需要偏移
                target_idx = self.source_item_num + target_id
                all_similarity_pairs.append((source_id, target_idx, similarity, "跨域"))

        # 2. 源域内相似边
        if hasattr(self, 'source_similar_pairs') and self.source_similar_pairs:
            for item1_id, item2_id, similarity in self.source_similar_pairs:
                if item1_id < self.source_item_num and item2_id < self.source_item_num:
                    all_similarity_pairs.append((item1_id, item2_id, similarity, "源域内"))

        # 3. 目标域内相似边
        if hasattr(self, 'target_similar_pairs') and self.target_similar_pairs:
            for item1_id, item2_id, similarity in self.target_similar_pairs:
                if item1_id < self.target_item_num and item2_id < self.target_item_num:
                    # 目标域物品索引需要偏移
                    item1_idx = self.source_item_num + item1_id
                    item2_idx = self.source_item_num + item2_id
                    all_similarity_pairs.append((item1_idx, item2_idx, similarity, "目标域内"))

        # 统一处理所有相似边（单向边）
        edge_counts = {"跨域": 0, "源域内": 0, "目标域内": 0}
        for idx1, idx2, similarity, edge_type in all_similarity_pairs:
            # 只添加单向边（较小索引指向较大索引）
            if idx1 < idx2:
                rows.append(idx1)
                cols.append(idx2)
                data.append(similarity)
            else:
                rows.append(idx2)
                cols.append(idx1)
                data.append(similarity)
            edge_counts[edge_type] += 1

        print(f"添加相似边: 跨域 {edge_counts['跨域']}, 源域内 {edge_counts['源域内']}, 目标域内 {edge_counts['目标域内']}")

        # 批量构建稀疏矩阵
        print("批量构建稀疏矩阵...")
        if len(rows) > 0:
            structural_graph = sp.coo_matrix((data, (rows, cols)),
                                           shape=(total_items, total_items),
                                           dtype=np.float32)
            # 转换为CSR格式以提高后续访问效率
            structural_graph = structural_graph.tocsr()
        else:
            structural_graph = sp.csr_matrix((total_items, total_items), dtype=np.float32)

        # 保存结果
        self.structural_similarity_graph = structural_graph

        print(f"结构相似图构建耗时: {time.time() - start_time:.2f}秒")

        print(f"物品结构相似图构建完成:")
        print(f"  - 总节点数: {total_items} (源域: {self.source_item_num}, 目标域: {self.target_item_num})")
        print(f"  - 总边数: {structural_graph.nnz} 条")
        print(f"  - 跨域相似边: {len(self.similar_item_pairs) * 2} 条")
        if hasattr(self, 'source_similar_pairs'):
            print(f"  - 源域内相似边: {len(self.source_similar_pairs) * 2} 条")
        if hasattr(self, 'target_similar_pairs'):
            print(f"  - 目标域内相似边: {len(self.target_similar_pairs) * 2} 条")

        return structural_graph
    
    def create_merged_kg_with_embeddings(self, source_kg_path, target_kg_path,
                                       source_entity_dict_path, target_entity_dict_path,
                                       similarity_threshold=0.5,
                                       dimensions=None, top_k=None):
        """
        创建合并的知识图谱并生成嵌入，统一处理所有相似度

        参数:
        - source_kg_path: 源域知识图谱文件路径
        - target_kg_path: 目标域知识图谱文件路径
        - source_entity_dict_path: 源域实体字典文件路径
        - target_entity_dict_path: 目标域实体字典文件路径
        - similarity_threshold: 嵌入相似度阈值，默认0.5
        - dimensions: Node2Vec嵌入维度，None表示使用默认值
        - top_k: 每个物品保留的最相似物品数量，None表示不限制

        返回:
        - structural_graph: 统一的结构相似图
        - similar_pairs: 所有相似物品对列表
        - source_embeddings: 源域物品嵌入
        - target_embeddings: 目标域物品嵌入
        """
        # 设置嵌入相似度阈值
        self.opt["struc_similarity_threshold"] = similarity_threshold
        print(f"开始创建知识图谱并生成嵌入，相似度阈值: {similarity_threshold}")

        # 1. 加载知识图谱数据
        self.load_kg_data(source_kg_path, target_kg_path)

        # 2. 加载实体字典
        self.load_entity_dictionaries(source_entity_dict_path, target_entity_dict_path)

        # 3. 构建合并的知识图谱
        self.build_merged_kg_graph()

        # 4. 生成节点嵌入（如果已存在嵌入文件则直接加载）
        self.generate_node2vec_embeddings(dimensions=dimensions)

        # 5. 计算物品相似度（统一处理）
        similar_pairs = self.calculate_item_similarity(
            threshold=similarity_threshold,
            top_k=top_k
        )

        # 6. 构建统一的结构相似图
        structural_graph = self.build_unified_structural_similarity_graph()

        print(f"Node2Vec结构化知识图谱创建完成:")
        print(f"  - 相似物品对总数: {len(similar_pairs)}")

        return (structural_graph, similar_pairs,
                self.source_embeddings, self.target_embeddings)