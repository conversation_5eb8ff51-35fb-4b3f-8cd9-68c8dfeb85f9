import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable
from model.CDRIB import CDRIB
from utils import torch_utils
import time
from utils.performance_analyzer import performance_analyzer

class Trainer(object):
    def __init__(self, opt):
        raise NotImplementedError

    def update(self, batch):
        raise NotImplementedError

    def predict(self, batch):
        raise NotImplementedError

    def update_lr(self, new_lr):
        torch_utils.change_lr(self.optimizer, new_lr)

    def set_current_epoch(self, epoch):
        """设置当前训练轮次，用于自适应权重调节"""
        self.current_epoch = epoch

    def load(self, filename):
        try:
            checkpoint = torch.load(filename)
        except BaseException:
            print("Cannot load model from {}".format(filename))
            exit()
        self.model.load_state_dict(checkpoint['model'])
        self.opt = checkpoint['config']

    def save(self, filename, epoch=None):
        params = {
            'model': self.model.state_dict(),
            'config': self.opt,
        }
        try:
            torch.save(params, filename)
            print("model saved to {}".format(filename))
        except BaseException:
            print("[Warning: Saving failed... continuing anyway.]")

class CrossTrainer(Trainer):
    def __init__(self, opt):
        self.opt = opt
        if self.opt["model"] == "CDRIB":
            self.model = CDRIB(opt)
        else :
            print("please input right model name!")
            exit(0)

        self.criterion = nn.BCEWithLogitsLoss()
        if opt['cuda']:
            self.model.cuda()
            self.criterion.cuda()
        self.optimizer = torch_utils.get_optimizer(opt['optim'], self.model.parameters(), opt['lr'], opt["weight_decay"])
        self.epoch_rec_loss = []

    def unpack_batch_predict(self, batch):
        if self.opt["cuda"]:
            inputs = [Variable(b.cuda()) for b in batch]
            user_index = inputs[0]
            item_index = inputs[1]
        else:
            inputs = [Variable(b) for b in batch]
            user_index = inputs[0]
            item_index = inputs[1]
        return user_index, item_index

    def unpack_batch(self, batch):
        if self.opt["cuda"]:
            inputs = [Variable(b.cuda()) for b in batch]
            source_user = inputs[0]
            source_pos_item = inputs[1]
            source_neg_item = inputs[2]
            target_user = inputs[3]
            target_pos_item = inputs[4]
            target_neg_item = inputs[5]
        else:
            inputs = [Variable(b) for b in batch]
            source_user = inputs[0]
            source_pos_item = inputs[1]
            source_neg_item = inputs[2]
            target_user = inputs[3]
            target_pos_item = inputs[4]
            target_neg_item = inputs[5]
        return source_user, source_pos_item, source_neg_item, target_user, target_pos_item, target_neg_item


    def source_predict(self, batch):
        user_index, item_index = self.unpack_batch_predict(batch)

        # 使用LightGCN用户嵌入和融合物品嵌入进行预测
        user_feature = self.my_index_select(self.source_user, user_index)

        # 获取融合后的物品表示
        item_feature = self.model.get_fused_item_embeddings(self.source_item, item_index.view(-1), domain="source")
        item_feature = item_feature.view(item_index.size()[0], item_index.size()[1], -1)

        user_feature = user_feature.view(user_feature.size()[0], 1, -1)
        user_feature = user_feature.repeat(1, item_feature.size()[1], 1)

        # 使用点积计算分数（与LightGCN的预测方式一致）
        score = (user_feature * item_feature).sum(dim=-1)
        return score.view(score.size()[0], score.size()[1])

    def target_predict(self, batch):
        user_index, item_index = self.unpack_batch_predict(batch)

        # 使用LightGCN用户嵌入和融合物品嵌入进行预测
        user_feature = self.my_index_select(self.target_user, user_index)

        # 获取融合后的物品表示
        item_feature = self.model.get_fused_item_embeddings(self.target_item, item_index.view(-1), domain="target")
        item_feature = item_feature.view(item_index.size()[0], item_index.size()[1], -1)

        user_feature = user_feature.view(user_feature.size()[0], 1, -1)
        user_feature = user_feature.repeat(1, item_feature.size()[1], 1)

        # 使用点积计算分数（与LightGCN的预测方式一致）
        score = (user_feature * item_feature).sum(dim=-1)
        return score.view(score.size()[0], score.size()[1])


    def my_index_select(self, memory, index):
        tmp = list(index.size()) + [-1]
        index = index.view(-1)
        ans = torch.index_select(memory, 0, index)
        ans = ans.view(tmp)
        return ans

    def evaluate_embedding(self, source_UV=None, source_VU=None, target_UV=None, target_VU=None, source_adj=None, target_adj=None, epoch=None):
        self.source_user, self.source_item, self.target_user, self.target_item = self.model(source_UV, source_VU,
                                                                                            target_UV, target_VU)

    def reconstruct_graph(self, batch, source_UV, source_VU, target_UV, target_VU, source_adj=None, target_adj=None):


        step_start = time.time()

        self.model.train()

        # 梯度清零时间
        performance_analyzer.start_timer('optimizer_zero_grad')
        self.optimizer.zero_grad()
        performance_analyzer.end_timer('optimizer_zero_grad')

        # 1. 数据解包时间
        performance_analyzer.start_timer('unpack_batch')
        source_user, source_pos_item, source_neg_item, target_user, target_pos_item, target_neg_item = self.unpack_batch(batch)
        unpack_time = performance_analyzer.end_timer('unpack_batch')

        # 2. 模型前向传播时间
        performance_analyzer.start_timer('model_forward')
        self.source_user, self.source_item, self.target_user, self.target_item = self.model(source_UV,source_VU, target_UV,target_VU, training_batch=batch)
        forward_time = performance_analyzer.end_timer('model_forward')

        # 记录各种对比学习损失的时间
        performance_analyzer.start_timer('contrastive_losses_total')
        struc_loss_time = getattr(self.model, '_struc_loss_time', 0)
        seman_loss_time = getattr(self.model, '_seman_loss_time', 0)
        cross_modal_loss_time = getattr(self.model, '_cross_modal_loss_time', 0)
        overlap_user_loss_time = getattr(self.model, '_overlap_user_loss_time', 0)
        performance_analyzer.end_timer('contrastive_losses_total')

        # 3. 融合嵌入计算时间
        performance_analyzer.start_timer('fusion_embeddings')
        # 获取融合后的物品表示
        source_pos_fused = self.model.get_fused_item_embeddings(self.source_item, source_pos_item, domain="source")
        source_neg_fused = self.model.get_fused_item_embeddings(self.source_item, source_neg_item, domain="source")
        target_pos_fused = self.model.get_fused_item_embeddings(self.target_item, target_pos_item, domain="target")
        target_neg_fused = self.model.get_fused_item_embeddings(self.target_item, target_neg_item, domain="target")
        fusion_time = performance_analyzer.end_timer('fusion_embeddings')

        # 4. BPR损失计算时间
        performance_analyzer.start_timer('bpr_loss_computation')
        # 使用融合表示计算BPR损失
        source_bpr_loss, source_reg_loss = self.model.source_lightgcn.bpr_loss(
            source_user, source_pos_item, source_neg_item,
            user_emb=None, pos_emb=source_pos_fused, neg_emb=source_neg_fused
        )
        target_bpr_loss, target_reg_loss = self.model.target_lightgcn.bpr_loss(
            target_user, target_pos_item, target_neg_item,
            user_emb=None, pos_emb=target_pos_fused, neg_emb=target_neg_fused
        )

        # LightGCN总损失
        lightgcn_loss = source_bpr_loss + target_bpr_loss
        lightgcn_reg = (source_reg_loss + target_reg_loss) * self.opt.get("lightgcn_reg", 1e-4)
        bpr_time = performance_analyzer.end_timer('bpr_loss_computation')


        # 5. 损失组合计算时间
        performance_analyzer.start_timer('loss_combination')
        # 获取损失权重系数
        item_contrastive_weight = self.opt.get("item_contrastive_weight", 1.0)
        user_CL_weight = self.opt.get("user_CL_weight", 1.0)
        lightgcn_weight = self.opt.get("lightgcn_weight", 1.0)


        # 计算重叠用户对比学习损失（替代对抗损失）
        overlap_user_loss = user_CL_weight * self.model.overlap_user_loss

        # 计算对比学习损失
        contrastive_loss = (item_contrastive_weight * self.model.item_struc_contrastive_loss
                          + item_contrastive_weight * self.model.item_seman_contrastive_loss)

        # 计算跨模态对比学习损失
        cross_modal_weight = self.opt.get("cross_modal_weight", 1.0)
        cross_modal_loss = cross_modal_weight * self.model.cross_modal_loss

        # 计算LightGCN损失
        lightgcn_total_loss = lightgcn_weight * (lightgcn_loss + lightgcn_reg)

        # 总损失（使用重叠用户对比学习替代对抗损失）
        loss = overlap_user_loss + contrastive_loss + lightgcn_total_loss + cross_modal_loss
        loss_combine_time = performance_analyzer.end_timer('loss_combination')

        # 6. 反向传播时间
        performance_analyzer.start_timer('backward_pass')
        loss.backward()
        backward_time = performance_analyzer.end_timer('backward_pass')

        # 7. 参数更新时间
        performance_analyzer.start_timer('optimizer_step')
        self.optimizer.step()
        optimizer_time = performance_analyzer.end_timer('optimizer_step')

        total_step_time = time.time() - step_start

        # 存储时间统计信息（用于调试）
        if not hasattr(self, 'time_stats'):
            self.time_stats = {
                'unpack': [],
                'forward': [],
                'fusion': [],
                'bpr': [],
                'loss_combine': [],
                'backward': [],
                'optimizer': [],
                'total': []
            }

        self.time_stats['unpack'].append(unpack_time)
        self.time_stats['forward'].append(forward_time)
        self.time_stats['fusion'].append(fusion_time)
        self.time_stats['bpr'].append(bpr_time)
        self.time_stats['loss_combine'].append(loss_combine_time)
        self.time_stats['backward'].append(backward_time)
        self.time_stats['optimizer'].append(optimizer_time)
        self.time_stats['total'].append(total_step_time)

        return loss.item()

    def get_time_statistics(self):
        """获取详细的时间统计信息"""
        if not hasattr(self, 'time_stats'):
            return None

        stats = {}
        for key, times in self.time_stats.items():
            if times:
                stats[key] = {
                    'total': sum(times),
                    'avg': sum(times) / len(times),
                    'max': max(times),
                    'min': min(times),
                    'count': len(times)
                }
        return stats

    def reset_time_statistics(self):
        """重置时间统计"""
        if hasattr(self, 'time_stats'):
            for key in self.time_stats:
                self.time_stats[key] = []