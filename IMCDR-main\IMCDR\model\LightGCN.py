import torch
import torch.nn as nn
import torch.nn.functional as F
import scipy.sparse as sp
import numpy as np


class LightGCN(nn.Module):
    """
    LightGCN模型实现
    用于处理用户-物品交互矩阵，生成用户和物品的嵌入表示
    """
    
    def __init__(self, num_users, num_items, embedding_dim, num_layers=3, dropout=0.1):
        """
        初始化LightGCN模型
        
        参数:
        - num_users: 用户数量
        - num_items: 物品数量  
        - embedding_dim: 嵌入维度
        - num_layers: GCN层数
        - dropout: dropout率
        """
        super(LightGCN, self).__init__()
        
        self.num_users = num_users
        self.num_items = num_items
        self.embedding_dim = embedding_dim
        self.num_layers = num_layers
        self.dropout = dropout
        
        # 用户和物品的初始嵌入
        self.user_embedding = nn.Embedding(num_users, embedding_dim)
        self.item_embedding = nn.Embedding(num_items, embedding_dim)
        
        # 初始化嵌入
        self._init_embeddings()
        
        # 存储图的邻接矩阵
        self.Graph = None
        
    def _init_embeddings(self):
        """初始化嵌入参数"""
        nn.init.xavier_uniform_(self.user_embedding.weight)
        nn.init.xavier_uniform_(self.item_embedding.weight)
        
    def create_adj_matrix(self, user_item_matrix):
        """
        创建用户-物品二部图的邻接矩阵

        参数:
        - user_item_matrix: 用户-物品交互矩阵 [num_users, num_items] (可以是稀疏或密集张量)

        返回:
        - 归一化的邻接矩阵 (PyTorch稀疏张量)
        """
        # 直接从PyTorch稀疏张量构建邻接矩阵，避免密集化
        if isinstance(user_item_matrix, torch.Tensor) and user_item_matrix.is_sparse:
            return self._create_adj_from_sparse_tensor(user_item_matrix)

        # 处理密集张量或numpy数组的情况
        if isinstance(user_item_matrix, torch.Tensor):
            user_item_matrix = user_item_matrix.cpu().numpy()

        # 构建二部图邻接矩阵
        # 格式: [[0, R], [R^T, 0]]
        # 其中R是用户-物品交互矩阵
        adj_matrix = sp.dok_matrix((self.num_users + self.num_items,
                                   self.num_users + self.num_items), dtype=np.float32)

        # 添加用户-物品连接
        adj_matrix[:self.num_users, self.num_users:] = user_item_matrix
        adj_matrix[self.num_users:, :self.num_users] = user_item_matrix.T
        
        # 转换为COO格式并归一化
        adj_matrix = adj_matrix.tocoo()
        
        # 计算度矩阵
        rowsum = np.array(adj_matrix.sum(axis=1)).flatten()
        d_inv_sqrt = np.power(rowsum + 1e-8, -0.5)
        d_inv_sqrt[np.isinf(d_inv_sqrt)] = 0.
        d_mat_inv_sqrt = sp.diags(d_inv_sqrt)
        
        # 归一化: D^(-1/2) * A * D^(-1/2)
        norm_adj = d_mat_inv_sqrt.dot(adj_matrix).dot(d_mat_inv_sqrt).tocoo()
        
        # 转换为PyTorch稀疏张量
        indices = torch.from_numpy(np.vstack((norm_adj.row, norm_adj.col)).astype(np.int64))
        values = torch.from_numpy(norm_adj.data)
        shape = norm_adj.shape
        
        return torch.sparse.FloatTensor(indices, values, shape)

    def _create_adj_from_sparse_tensor(self, sparse_ui_matrix):
        """
        直接从稀疏张量创建邻接矩阵，避免密集化

        参数:
        - sparse_ui_matrix: 稀疏的用户-物品交互矩阵

        返回:
        - 归一化的邻接矩阵 (PyTorch稀疏张量)
        """
        device = sparse_ui_matrix.device

        # 获取稀疏矩阵的索引和值
        indices = sparse_ui_matrix._indices()  # [2, nnz]
        values = sparse_ui_matrix._values()    # [nnz]

        # 构建二部图的索引
        # 用户-物品连接: (user_idx, item_idx + num_users)
        ui_indices = torch.stack([
            indices[0],  # 用户索引
            indices[1] + self.num_users  # 物品索引偏移
        ])

        # 物品-用户连接: (item_idx + num_users, user_idx)
        iu_indices = torch.stack([
            indices[1] + self.num_users,  # 物品索引偏移
            indices[0]   # 用户索引
        ])

        # 合并所有连接
        all_indices = torch.cat([ui_indices, iu_indices], dim=1)
        all_values = torch.cat([values, values])  # 对称矩阵，值重复

        # 创建邻接矩阵
        adj_size = self.num_users + self.num_items
        adj_matrix = torch.sparse_coo_tensor(
            all_indices, all_values,
            (adj_size, adj_size),
            device=device
        ).coalesce()

        # 计算度矩阵进行归一化
        # 计算每个节点的度
        row_sum = torch.sparse.sum(adj_matrix, dim=1).to_dense()
        d_inv_sqrt = torch.pow(row_sum + 1e-8, -0.5)
        d_inv_sqrt[torch.isinf(d_inv_sqrt)] = 0.

        # 创建度矩阵的稀疏表示
        d_indices = torch.arange(adj_size, device=device).unsqueeze(0).repeat(2, 1)
        d_matrix = torch.sparse_coo_tensor(
            d_indices, d_inv_sqrt,
            (adj_size, adj_size),
            device=device
        )

        # 归一化: D^(-1/2) * A * D^(-1/2)
        norm_adj = torch.sparse.mm(torch.sparse.mm(d_matrix, adj_matrix), d_matrix)

        return norm_adj.coalesce()

    def forward(self, user_item_matrix, users=None, items=None):
        """
        LightGCN前向传播
        
        参数:
        - user_item_matrix: 用户-物品交互矩阵
        - users: 用户索引（可选，用于只计算特定用户）
        - items: 物品索引（可选，用于只计算特定物品）
        
        返回:
        - user_embeddings: 用户嵌入
        - item_embeddings: 物品嵌入
        """
        # 创建或更新邻接矩阵
        if self.Graph is None:
            self.Graph = self.create_adj_matrix(user_item_matrix)
            # 确保图与输入张量在同一设备（稀疏张量处理中已经处理了设备）
            if isinstance(user_item_matrix, torch.Tensor) and user_item_matrix.is_cuda and not self.Graph.is_cuda:
                self.Graph = self.Graph.cuda()
        
        # 获取初始嵌入
        all_embeddings = torch.cat([self.user_embedding.weight, 
                                   self.item_embedding.weight], dim=0)
        
        embeddings_list = [all_embeddings]
        
        # 多层图卷积
        for layer in range(self.num_layers):
            all_embeddings = torch.sparse.mm(self.Graph, all_embeddings)
            if self.training:
                all_embeddings = F.dropout(all_embeddings, self.dropout)
            embeddings_list.append(all_embeddings)
        
        # 平均所有层的嵌入
        final_embeddings = torch.stack(embeddings_list, dim=1).mean(dim=1)
        
        # 分离用户和物品嵌入
        user_embeddings = final_embeddings[:self.num_users]
        item_embeddings = final_embeddings[self.num_users:]
        
        # 如果指定了特定的用户或物品索引
        if users is not None:
            user_embeddings = user_embeddings[users]
        if items is not None:
            item_embeddings = item_embeddings[items]
            
        return user_embeddings, item_embeddings
    
    def predict(self, users, items):
        """
        预测用户对物品的偏好分数
        
        参数:
        - users: 用户索引
        - items: 物品索引
        
        返回:
        - 预测分数
        """
        user_emb = self.user_embedding(users)
        item_emb = self.item_embedding(items)
        
        # 计算内积作为预测分数
        scores = torch.sum(user_emb * item_emb, dim=1)
        return scores
    
    def bpr_loss(self, users, pos_items, neg_items, user_emb=None, pos_emb=None, neg_emb=None):
        """
        计算BPR损失
        
        参数:
        - users: 用户索引
        - pos_items: 正样本物品索引
        - neg_items: 负样本物品索引
        - user_emb: 用户嵌入（可选）
        - pos_emb: 正样本物品嵌入（可选）
        - neg_emb: 负样本物品嵌入（可选）
        
        返回:
        - BPR损失
        """
        if user_emb is None:
            user_emb = self.user_embedding(users)
        if pos_emb is None:
            pos_emb = self.item_embedding(pos_items)
        if neg_emb is None:
            neg_emb = self.item_embedding(neg_items)
        
        # 计算正负样本分数
        pos_scores = torch.sum(user_emb * pos_emb, dim=1)
        neg_scores = torch.sum(user_emb * neg_emb, dim=1)
        
        # BPR损失
        bpr_loss = -torch.log(torch.sigmoid(pos_scores - neg_scores) + 1e-8).mean()
        
        # L2正则化
        reg_loss = (user_emb.norm(2).pow(2) + 
                   pos_emb.norm(2).pow(2) + 
                   neg_emb.norm(2).pow(2)) / 2
        
        return bpr_loss, reg_loss
    
    def get_embedding_weights(self):
        """获取当前的嵌入权重"""
        return self.user_embedding.weight, self.item_embedding.weight
