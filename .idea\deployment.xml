<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" serverName="<EMAIL>:29813 password" remoteFilesAllowedToDisappearOnAutoupload="false">
    <serverData>
      <paths name="<EMAIL>:22136 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:24634 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:26793 password">
        <serverdata>
          <mappings>
            <mapping deploy="/root/autodl-tmp/IMCDR" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:37918 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:14055 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:15363 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:20207 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:29813 password">
        <serverdata>
          <mappings>
            <mapping deploy="/root/autodl-tmp/CDRIB-v5.3" local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:24099 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
    </serverData>
  </component>
</project>