import torch
import torch.nn as nn
import torch.nn.functional as F
from model.LightGCN import LightGCN
import time


class CDRIB(nn.Module):
    def __init__(self, opt):
        super(CDRIB, self).__init__()
        self.opt=opt

        # 用LightGCN替换VBGE

        # self.dis = nn.Bilinear(opt["feature_dim"], opt["feature_dim"], 1)
        self.item_temperature = opt.get("item_temperature", 0.1)
        self.cross_modal_temperature = opt.get("cross_modal_temperature", 0.1)

        # 重叠用户对比学习的温度参数
        self.overlap_user_temperature = opt.get("overlap_user_temperature", 0.1)

        # 对比学习投影头 - LightGCN输出是单一维度，不需要降维
        self.projection = nn.Sequential(
            nn.Linear(opt["feature_dim"], opt["feature_dim"]),
            nn.ReLU(),
            nn.Linear(opt["feature_dim"], opt["feature_dim"])
        )

        # 物品表示融合层：融合LightGCN、结构和语义嵌入
        self.item_fusion = nn.Sequential(
            nn.Linear(opt["feature_dim"] * 3, opt["feature_dim"] * 2),  # 3种嵌入融合
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(opt["feature_dim"] * 2, opt["feature_dim"])  # 输出与原始维度一致
        )


        # 删除独立的嵌入层，直接使用LightGCN的嵌入
        # self.source_user_embedding = nn.Embedding(opt["source_user_num"], opt["feature_dim"])
        # self.target_user_embedding = nn.Embedding(opt["target_user_num"], opt["feature_dim"])
        # self.source_item_embedding = nn.Embedding(opt["source_item_num"], opt["feature_dim"])
        # self.target_item_embedding = nn.Embedding(opt["target_item_num"], opt["feature_dim"])

        # 保留索引用于其他功能
        self.source_user_index = torch.arange(0, self.opt["source_user_num"], 1)
        self.target_user_index = torch.arange(0, self.opt["target_user_num"], 1)
        self.source_item_index = torch.arange(0, self.opt["source_item_num"], 1)
        self.target_item_index = torch.arange(0, self.opt["target_item_num"], 1)

        # 预训练嵌入存储（将在set_pretrained_embeddings中设置）
        self.node2vec_embeddings = None  # Node2Vec结构嵌入
        self.word2vec_embeddings = None  # Word2Vec语义嵌入

        # 预处理的投影嵌入（避免重复投影计算）
        self.projected_node2vec_embeddings = None  # 投影后的结构嵌入
        self.projected_word2vec_embeddings = None  # 投影后的语义嵌入

        # 调试信息控制
        self.debug_epoch_counter = 0  # 用于控制调试信息的epoch计数器
        self.debug_printed_this_epoch = False  # 标记当前epoch是否已打印调试信息

        # 内存管理配置
        self.max_contrastive_batch_size = opt.get('max_contrastive_batch_size', 512)  # 对比学习最大批次大小

        # 新的批次采样策略参数
        self.cross_modal_batch_size = opt.get('cross_modal_batch_size', 256)  # 跨模态对比学习批次大小
        self.similar_pairs_batch_size = opt.get('similar_pairs_batch_size', 512)  # 相似物品对批次大小

        # 直接注册为buffer，确保在模型移动设备时自动移动
        if isinstance(self.opt["overlap_users_id"], torch.Tensor):
            overlap_user_tensor = self.opt["overlap_users_id"].clone().detach().long()
        else:
            overlap_user_tensor = torch.tensor(self.opt["overlap_users_id"], dtype=torch.long)

        self.register_buffer('overlap_user_index', overlap_user_tensor)

        # LightGCN模块
        self.source_lightgcn = LightGCN(
            num_users=self.opt["source_user_num"],
            num_items=self.opt["source_item_num"],
            embedding_dim=self.opt["feature_dim"],
            num_layers=self.opt.get("lightgcn_layers", 3),
            dropout=self.opt.get("lightgcn_dropout", 0.1)
        )

        self.target_lightgcn = LightGCN(
            num_users=self.opt["target_user_num"],
            num_items=self.opt["target_item_num"],
            embedding_dim=self.opt["feature_dim"],
            num_layers=self.opt.get("lightgcn_layers", 3),
            dropout=self.opt.get("lightgcn_dropout", 0.1)
        )

        # 移除手动CUDA移动，register_buffer会自动处理设备移动
        # 模型的子模块会通过 model.cuda() 自动移动到GPU


    def set_similarity_graphs(self, structural_graph, semantic_graph):
        """
        设置统一的相似图，用于基于图连边的对比学习
        优化版本：预构建全局相似度张量，避免每个batch重复构建

        参数:
        - structural_graph: 结构相似图（稀疏矩阵）
        - semantic_graph: 语义相似图（稀疏矩阵）
        """
        self.structural_graph = structural_graph
        self.semantic_graph = semantic_graph

        # 将稀疏矩阵转换为张量（如果需要GPU计算）
        if self.opt["cuda"]:
            # 转换为COO格式以便转换为张量
            structural_coo = structural_graph.tocoo()
            semantic_coo = semantic_graph.tocoo()

            self.structural_edges = torch.stack([
                torch.from_numpy(structural_coo.row).long(),
                torch.from_numpy(structural_coo.col).long()
            ]).cuda()
            self.structural_weights = torch.from_numpy(structural_coo.data).float().cuda()

            self.semantic_edges = torch.stack([
                torch.from_numpy(semantic_coo.row).long(),
                torch.from_numpy(semantic_coo.col).long()
            ]).cuda()
            self.semantic_weights = torch.from_numpy(semantic_coo.data).float().cuda()

        # 预构建全局相似度张量（优化关键）
        self._precompute_global_similarity_tensors()

        print(f"设置相似图完成:")
        print(f"  - 结构相似图: {len(self.structural_weights)} 条边")
        print(f"  - 语义相似图: {len(self.semantic_weights)} 条边")
        print(f"  - 已预构建全局相似度张量，训练时将使用高效切片访问")

    def _precompute_global_similarity_tensors(self):
        """
        预构建全局相似度张量，避免每个batch重复构建
        这是性能优化的关键：将 O(edges * batches) 的复杂度降低到 O(edges)
        """
        print("预构建全局相似度张量...")

        # 获取总物品数
        if hasattr(self, 'node2vec_embeddings') and self.node2vec_embeddings is not None:
            total_items = self.node2vec_embeddings.size(0)
        elif hasattr(self, 'word2vec_embeddings') and self.word2vec_embeddings is not None:
            total_items = self.word2vec_embeddings.size(0)
        else:
            total_items = self.opt['source_item_num'] + self.opt['target_item_num']

        device = next(self.parameters()).device

        # 预构建结构相似度张量
        self.global_structural_similarity = torch.zeros(total_items, total_items, device=device, dtype=torch.bool)
        if hasattr(self, 'structural_edges') and self.structural_edges is not None:
            edges = self.structural_edges
            # 批量设置连边
            self.global_structural_similarity[edges[0], edges[1]] = True
            self.global_structural_similarity[edges[1], edges[0]] = True  # 对称矩阵

        # 预构建语义相似度张量
        self.global_semantic_similarity = torch.zeros(total_items, total_items, device=device, dtype=torch.bool)
        if hasattr(self, 'semantic_edges') and self.semantic_edges is not None:
            edges = self.semantic_edges
            # 批量设置连边
            self.global_semantic_similarity[edges[0], edges[1]] = True
            self.global_semantic_similarity[edges[1], edges[0]] = True  # 对称矩阵

        print(f"全局相似度张量构建完成:")
        print(f"  - 结构相似度张量: {self.global_structural_similarity.shape}")
        print(f"  - 语义相似度张量: {self.global_semantic_similarity.shape}")
        print(f"  - 内存占用: {(self.global_structural_similarity.numel() + self.global_semantic_similarity.numel()) * 1 / 1024 / 1024:.2f} MB")

    def get_batch_similarity_matrix(self, graph_type, batch_indices):
        """
        高效的批次相似度矩阵获取 - 使用预构建张量的切片访问
        替换原来的 build_batch_similarity_matrix，性能提升显著

        参数:
        - graph_type: "structural" 或 "semantic"
        - batch_indices: 批次物品索引 [batch_size]

        返回:
        - similarity_matrix: 批次内相似度矩阵 [batch_size, batch_size]
        """
        # 只在每个epoch第一次调用时打印调试信息
        if not self.debug_printed_this_epoch:
            print(f"    [DEBUG] get_batch_similarity_matrix - graph_type: {graph_type}")
            print(f"    [DEBUG] batch_indices shape: {batch_indices.shape}")

            if graph_type == "structural":
                global_sim = self.global_structural_similarity
                print(f"    [DEBUG] global_structural_similarity shape: {global_sim.shape if global_sim is not None else None}")
                print(f"    [DEBUG] global_structural_similarity 非零元素: {global_sim.sum().item() if global_sim is not None else 0}")
            else:
                global_sim = self.global_semantic_similarity
                print(f"    [DEBUG] global_semantic_similarity shape: {global_sim.shape if global_sim is not None else None}")
                print(f"    [DEBUG] global_semantic_similarity 非零元素: {global_sim.sum().item() if global_sim is not None else 0}")
        else:
            if graph_type == "structural":
                global_sim = self.global_structural_similarity
            else:
                global_sim = self.global_semantic_similarity

        # 高效切片访问：O(1) 复杂度
        result = global_sim[batch_indices][:, batch_indices]
        if not self.debug_printed_this_epoch:
            print(f"    [DEBUG] 返回的相似度矩阵形状: {result.shape}")
            print(f"    [DEBUG] 返回的相似度矩阵非零元素: {result.sum().item()}")
        return result

    def get_batch_edge_matrix(self, graph_type, batch_indices):
        """
        高效的批次连边矩阵获取 - 使用预构建张量的切片访问
        替换原来的 build_batch_edge_matrix，性能提升显著

        参数:
        - graph_type: "structural" 或 "semantic"
        - batch_indices: 批次物品索引 [batch_size]

        返回:
        - edge_matrix: 批次内连边矩阵 [batch_size, batch_size]
        """
        if graph_type == "structural":
            global_sim = self.global_structural_similarity
        else:
            global_sim = self.global_semantic_similarity

        # 高效切片访问：O(1) 复杂度
        return global_sim[batch_indices][:, batch_indices]

    def get_similar_item_pairs(self, graph_type):
        """
        获取相似物品对列表

        参数:
        - graph_type: "structural" 或 "semantic"

        返回:
        - pairs: 相似物品对的索引列表 [(i, j), ...]
        """
        if graph_type == "structural":
            edges = self.structural_edges
        else:
            edges = self.semantic_edges

        if edges is None:
            return []

        # 将边转换为物品对列表
        pairs = []
        for i in range(edges.size(1)):
            item_i, item_j = edges[0, i].item(), edges[1, i].item()
            pairs.append((item_i, item_j))

        return pairs

    def set_similarity_graphs_from_edges(self, structural_edges, structural_weights, semantic_edges, semantic_weights):
        """
        直接从边和权重设置相似图（用于测试）
        优化版本：同样支持预构建全局相似度张量

        参数:
        - structural_edges: 结构相似图的边 [2, num_edges]
        - structural_weights: 结构相似图的权重 [num_edges]
        - semantic_edges: 语义相似图的边 [2, num_edges]
        - semantic_weights: 语义相似图的权重 [num_edges]
        """
        self.structural_edges = structural_edges
        self.structural_weights = structural_weights
        self.semantic_edges = semantic_edges
        self.semantic_weights = semantic_weights

        if self.opt["cuda"]:
            self.structural_edges = self.structural_edges.cuda()
            self.structural_weights = self.structural_weights.cuda()
            self.semantic_edges = self.semantic_edges.cuda()
            self.semantic_weights = self.semantic_weights.cuda()

        # 预构建全局相似度张量
        self._precompute_global_similarity_tensors()

    def set_pretrained_embeddings(self, node2vec_embeddings, word2vec_embeddings):
        """
        设置预训练的Node2Vec和Word2Vec嵌入，并预处理投影

        优化：由于嵌入是静态的，预处理一次投影，避免每个epoch重复计算

        参数:
        - node2vec_embeddings: Node2Vec嵌入 [total_items, embedding_dim]
        - word2vec_embeddings: Word2Vec嵌入 [total_items, embedding_dim]
        """
        self.node2vec_embeddings = torch.FloatTensor(node2vec_embeddings)
        self.word2vec_embeddings = torch.FloatTensor(word2vec_embeddings)

        if self.opt["cuda"]:
            self.node2vec_embeddings = self.node2vec_embeddings.cuda()
            self.word2vec_embeddings = self.word2vec_embeddings.cuda()

        # 预处理投影：一次性计算所有嵌入的投影，避免训练时重复计算
        print("预处理嵌入投影...")
        self._preprocess_embeddings_projection()

        print(f"设置预训练嵌入完成:")
        print(f"  - Node2Vec嵌入: {self.node2vec_embeddings.shape}")
        print(f"  - Word2Vec嵌入: {self.word2vec_embeddings.shape}")
        print(f"  - 预处理投影完成，训练时将直接使用投影后的嵌入")

    def _preprocess_embeddings_projection(self):
        """
        预处理嵌入投影：一次性计算所有静态嵌入的投影

        优化关键：
        - Node2Vec 和 Word2Vec 嵌入是静态的，不会在训练中改变
        - 投影层的参数在训练中会更新，但可以在每个epoch开始时重新计算投影
        - 避免每个batch都重复计算相同的投影操作
        """
        print(f"[DEBUG] _preprocess_embeddings_projection 开始")
        print(f"[DEBUG] node2vec_embeddings is None: {self.node2vec_embeddings is None}")
        print(f"[DEBUG] word2vec_embeddings is None: {self.word2vec_embeddings is None}")

        if self.node2vec_embeddings is None or self.word2vec_embeddings is None:
            print("警告：预训练嵌入未设置，跳过投影预处理")
            return

        print("开始预处理嵌入投影...")
        print(f"[DEBUG] node2vec_embeddings shape: {self.node2vec_embeddings.shape}")
        print(f"[DEBUG] word2vec_embeddings shape: {self.word2vec_embeddings.shape}")
        print(f"[DEBUG] projection layer: {self.projection}")

        try:
            with torch.no_grad():  # 预处理时不需要梯度
                # 投影 Node2Vec 嵌入（结构嵌入）
                print(f"[DEBUG] 开始投影 Node2Vec 嵌入...")
                projected_node2vec = self.projection(self.node2vec_embeddings)
                self.projected_node2vec_embeddings = F.normalize(projected_node2vec, dim=1)
                print(f"[DEBUG] Node2Vec 投影完成，形状: {self.projected_node2vec_embeddings.shape}")

                # 投影 Word2Vec 嵌入（语义嵌入）
                print(f"[DEBUG] 开始投影 Word2Vec 嵌入...")
                projected_word2vec = self.projection(self.word2vec_embeddings)
                self.projected_word2vec_embeddings = F.normalize(projected_word2vec, dim=1)
                print(f"[DEBUG] Word2Vec 投影完成，形状: {self.projected_word2vec_embeddings.shape}")

            print(f"投影预处理完成:")
            print(f"  - 投影后Node2Vec嵌入: {self.projected_node2vec_embeddings.shape}")
            print(f"  - 投影后Word2Vec嵌入: {self.projected_word2vec_embeddings.shape}")
            print(f"  - 内存节省: 避免每个batch重复投影计算")

        except Exception as e:
            print(f"[ERROR] 投影预处理失败: {e}")
            print(f"[ERROR] 将 projected_embeddings 设置为 None")
            self.projected_node2vec_embeddings = None
            self.projected_word2vec_embeddings = None

    def update_projected_embeddings(self):
        """
        更新投影嵌入：当投影层参数更新后，重新计算投影

        注意：这个方法应该在每个epoch开始时调用，或者在投影层参数显著更新后调用
        """
        if self.node2vec_embeddings is not None and self.word2vec_embeddings is not None:
            self._preprocess_embeddings_projection()

    def set_debug_epoch(self, epoch):
        """
        设置当前epoch，用于控制调试信息输出

        参数:
        - epoch: 当前epoch数
        """
        if epoch != self.debug_epoch_counter:
            self.debug_epoch_counter = epoch
            self.debug_printed_this_epoch = False



    def cross_modal_contrastive_loss(self, selected_items, batch_indices=None):
        """
        跨模态对比学习损失：让同一物品的结构嵌入和语义嵌入更相似

        新策略：随机选择一批物品，每个物品的两个视图表示（结构嵌入、语义嵌入）进行对比

        核心思想：
        - 正样本：同一物品的结构嵌入 vs 语义嵌入
        - 负样本：批次内其他物品的嵌入

        参数:
        - selected_items: 选中的物品索引（已弃用，保持接口兼容性）
        - batch_indices: 训练批次中的物品索引（已弃用，保持接口兼容性）

        返回:
        - 跨模态对比学习损失
        """
        if self.node2vec_embeddings is None or self.word2vec_embeddings is None:
            return torch.tensor(0.0, device=next(self.parameters()).device, requires_grad=True)

        device = next(self.parameters()).device
        total_items = self.node2vec_embeddings.size(0)

        # 随机选择一批物品进行跨模态对比学习
        batch_size = min(self.cross_modal_batch_size, total_items)
        if batch_size < 2:
            return torch.tensor(0.0, device=device, requires_grad=True)

        # 随机采样物品索引
        item_indices = torch.randperm(total_items, device=device)[:batch_size]

        # 批量获取结构和语义嵌入
        structural_embs = self.node2vec_embeddings[item_indices]  # [batch_size, embedding_dim]
        semantic_embs = self.word2vec_embeddings[item_indices]    # [batch_size, embedding_dim]

        # 归一化嵌入
        structural_embs = F.normalize(structural_embs, dim=1)
        semantic_embs = F.normalize(semantic_embs, dim=1)

        # 计算所有跨模态相似度矩阵
        struct_to_seman_sim = torch.mm(structural_embs, semantic_embs.T) / self.cross_modal_temperature
        seman_to_struct_sim = torch.mm(semantic_embs, structural_embs.T) / self.cross_modal_temperature

        # 简化的InfoNCE损失计算：每个物品的两个视图作为正样本对
        # 1. 构建负样本掩码矩阵：排除对角线（自己）
        diag_mask = torch.eye(batch_size, device=structural_embs.device).bool()
        neg_mask = ~diag_mask

        # 2. 计算正样本分数（对角线元素）
        pos_scores_1 = torch.diag(struct_to_seman_sim)  # 结构→语义
        pos_scores_2 = torch.diag(seman_to_struct_sim)  # 语义→结构

        # 3. 计算负样本分数和
        # 将对角线位置设为极小值，这样exp后接近0
        masked_sim_1 = struct_to_seman_sim.clone()
        masked_sim_2 = seman_to_struct_sim.clone()
        masked_sim_1[diag_mask] = -1e9
        masked_sim_2[diag_mask] = -1e9

        # 计算每行的负样本exp和
        neg_exp_sums_1 = torch.sum(torch.exp(masked_sim_1), dim=1)
        neg_exp_sums_2 = torch.sum(torch.exp(masked_sim_2), dim=1)

        # 4. 计算InfoNCE损失
        pos_exp_1 = torch.exp(pos_scores_1)
        pos_exp_2 = torch.exp(pos_scores_2)

        loss_1 = -torch.log(pos_exp_1 / (pos_exp_1 + neg_exp_sums_1 + 1e-8))
        loss_2 = -torch.log(pos_exp_2 / (pos_exp_2 + neg_exp_sums_2 + 1e-8))

        # 平均两个方向的损失
        total_loss = ((loss_1 + loss_2) / 2).mean()

        return total_loss

    def overlap_user_contrastive_loss(self, source_users, target_users, overlap_indices, training_batch=None):
        """
        重叠用户对比学习损失：让同一用户在两个域的表示更相似
        直接使用训练批次内的重叠用户，不再额外限制批次大小

        参数:
        - source_users: 源域用户嵌入 [num_users, feature_dim]
        - target_users: 目标域用户嵌入 [num_users, feature_dim]
        - overlap_indices: 重叠用户索引
        - training_batch: 训练批次信息

        返回:
        - 重叠用户对比学习损失
        """
        if len(overlap_indices) == 0:
            return torch.tensor(0.0, device=source_users.device, requires_grad=True)

        # overlap_indices 现在是 register_buffer，会自动在正确设备上
        device = source_users.device

        # 如果有训练批次信息，优先使用训练批次中的重叠用户
        if training_batch is not None:
            batch_overlap_users = self.get_batch_overlap_users(training_batch, overlap_indices)
            if len(batch_overlap_users) > 0:
                sampled_overlap_users = batch_overlap_users
            else:
                # 如果训练批次中没有重叠用户，使用所有重叠用户
                sampled_overlap_users = overlap_indices
        else:
            # 直接使用所有重叠用户，不再限制批次大小
            sampled_overlap_users = overlap_indices

        batch_size = len(sampled_overlap_users)

        # 获取采样用户的嵌入
        source_overlap_emb = source_users[sampled_overlap_users]  # [batch_size, feature_dim]
        target_overlap_emb = target_users[sampled_overlap_users]  # [batch_size, feature_dim]

        # L2归一化
        source_overlap_emb = F.normalize(source_overlap_emb, dim=1)
        target_overlap_emb = F.normalize(target_overlap_emb, dim=1)

        # 计算正样本相似度（同一用户在两域的表示）
        pos_sim = F.cosine_similarity(source_overlap_emb, target_overlap_emb, dim=1) / self.overlap_user_temperature

        # 矩阵化 InfoNCE 损失计算
        # 1. 计算所有用户对之间的跨域相似度矩阵
        source_to_target_sim = torch.mm(source_overlap_emb, target_overlap_emb.T) / self.overlap_user_temperature  # [batch_size, batch_size]
        target_to_source_sim = torch.mm(target_overlap_emb, source_overlap_emb.T) / self.overlap_user_temperature  # [batch_size, batch_size]

        # 2. 构建负样本掩码（排除对角线，即排除自己）
        diag_mask = torch.eye(batch_size, device=source_overlap_emb.device).bool()
        neg_mask = ~diag_mask  # [batch_size, batch_size]

        # 3. 提取正样本分数（对角线元素）
        pos_scores = torch.exp(pos_sim)  # [batch_size]

        # 4. 计算负样本分数
        # 源域用户 vs 其他目标域用户的分数
        neg_scores_s2t = torch.exp(source_to_target_sim) * neg_mask.float()  # [batch_size, batch_size]
        # 目标域用户 vs 其他源域用户的分数
        neg_scores_t2s = torch.exp(target_to_source_sim) * neg_mask.float()  # [batch_size, batch_size]

        # 5. 对每个用户，计算其负样本分数和
        neg_sums_s2t = neg_scores_s2t.sum(dim=1)  # [batch_size] - 每个源域用户的负样本分数和
        neg_sums_t2s = neg_scores_t2s.sum(dim=1)  # [batch_size] - 每个目标域用户的负样本分数和

        # 总的负样本分数和（两个方向）
        total_neg_sums = neg_sums_s2t + neg_sums_t2s  # [batch_size]

        # 6. 矩阵化 InfoNCE 损失计算
        losses = -torch.log(pos_scores / (pos_scores + total_neg_sums + 1e-8))  # [batch_size]

        # 7. 返回平均损失
        return losses.mean()


    def graph_based_contrastive_loss(self, graph_type="structural", batch_indices=None):
        """
        域间对比学习损失 - 结构相似物品对要求对应语义表示为正样本对，语义上反之

        核心思想：
        - 结构对比学习：使用结构相似的物品对作为正样本，但在语义表示空间中进行对比学习
        - 语义对比学习：使用语义相似的物品对作为正样本，但在结构表示空间中进行对比学习

        参数:
        - graph_type: 图类型 ("structural" 或 "semantic")
        - batch_indices: 训练批次中的物品索引，如果为None则使用所有物品

        返回:
        - loss: 对比学习损失
        """
        # === 调试信息开始（每个epoch只打印一次）===
        if not self.debug_printed_this_epoch:
            print(f"\n[DEBUG] Epoch {self.debug_epoch_counter} - graph_based_contrastive_loss 调用:")
            print(f"  - graph_type: {graph_type}")
            print(f"  - batch_indices: {batch_indices.shape if batch_indices is not None else None}")
            print(f"  - projected_word2vec_embeddings is None: {self.projected_word2vec_embeddings is None}")
            print(f"  - projected_node2vec_embeddings is None: {self.projected_node2vec_embeddings is None}")
            if self.projected_word2vec_embeddings is not None:
                print(f"  - projected_word2vec_embeddings shape: {self.projected_word2vec_embeddings.shape}")
            if self.projected_node2vec_embeddings is not None:
                print(f"  - projected_node2vec_embeddings shape: {self.projected_node2vec_embeddings.shape}")
        # === 调试信息结束 ===
        if graph_type == "structural":
            # 结构对比学习：使用结构相似图的连边作为正样本对，但在语义表示空间中计算损失
            # 直接使用预处理的投影语义嵌入（Word2Vec）
            if not self.debug_printed_this_epoch:
                print(f"  [DEBUG] 结构对比学习 - 检查 projected_word2vec_embeddings")
            if self.projected_word2vec_embeddings is None:
                if not self.debug_printed_this_epoch:
                    print(f"  [DEBUG] projected_word2vec_embeddings 为 None，返回零损失")
                return torch.tensor(0.0, device=next(self.parameters()).device, requires_grad=True)
            projected_features = self.projected_word2vec_embeddings
            if not self.debug_printed_this_epoch:
                print(f"  [DEBUG] 使用 projected_word2vec_embeddings，形状: {projected_features.shape}")
        else:
            # 语义对比学习：使用语义相似图的连边作为正样本对，但在结构表示空间中计算损失
            # 直接使用预处理的投影结构嵌入（Node2Vec）
            if not self.debug_printed_this_epoch:
                print(f"  [DEBUG] 语义对比学习 - 检查 projected_node2vec_embeddings")
            if self.projected_node2vec_embeddings is None:
                if not self.debug_printed_this_epoch:
                    print(f"  [DEBUG] projected_node2vec_embeddings 为 None，返回零损失")
                return torch.tensor(0.0, device=next(self.parameters()).device, requires_grad=True)
            projected_features = self.projected_node2vec_embeddings
            if not self.debug_printed_this_epoch:
                print(f"  [DEBUG] 使用 projected_node2vec_embeddings，形状: {projected_features.shape}")

        # 使用预处理的投影嵌入，无需重复投影和归一化

        total_items = projected_features.size(0)

        # 限制批次大小以避免内存溢出
        if batch_indices is None:
            batch_indices = torch.arange(min(total_items, self.max_contrastive_batch_size)).to(projected_features.device)
        elif len(batch_indices) > self.max_contrastive_batch_size:
            # 如果批次太大，随机采样以控制内存使用
            perm = torch.randperm(len(batch_indices))[:self.max_contrastive_batch_size]
            batch_indices = batch_indices[perm]

        # 选择当前批次的物品特征
        batch_features = projected_features[batch_indices]  # [batch_size, feature_dim]
        batch_size = len(batch_indices)

        if batch_size < 2:
            return torch.tensor(0.0, device=projected_features.device, requires_grad=True)

        # 高效获取批次内的相似度矩阵（使用预构建张量切片）
        similarity_matrix = self.get_batch_similarity_matrix(graph_type, batch_indices)
        if not self.debug_printed_this_epoch:
            print(f"  [DEBUG] 相似度矩阵形状: {similarity_matrix.shape}")
            print(f"  [DEBUG] 相似度矩阵中非零元素数量: {(similarity_matrix > 0).sum().item()}")

        # 计算所有物品对的分数矩阵
        score_matrix = torch.mm(batch_features, batch_features.T) / self.item_temperature  # [batch_size, batch_size]

        # 使用相似度矩阵区分正负样本
        pos_pairs = similarity_matrix > 0  # 正样本对（有连边）
        if not self.debug_printed_this_epoch:
            print(f"  [DEBUG] 初始正样本对数量: {pos_pairs.sum().item()}")

        # 移除对角线（自己与自己的相似度）
        # 确保diag_mask与pos_pairs在同一设备上
        diag_mask = torch.eye(batch_size, device=pos_pairs.device).bool()
        pos_pairs = pos_pairs & (~diag_mask)
        if not self.debug_printed_this_epoch:
            print(f"  [DEBUG] 移除对角线后正样本对数量: {pos_pairs.sum().item()}")

        # 检查是否有正样本对
        if not pos_pairs.any():
            if not self.debug_printed_this_epoch:
                print(f"  [DEBUG] 没有正样本对，返回零损失")
            return torch.tensor(0.0, device=projected_features.device, requires_grad=True)

        # 标记本epoch已打印调试信息
        if not self.debug_printed_this_epoch:
            self.debug_printed_this_epoch = True

        # 对每个有正样本的物品计算对比损失
        pos_items = torch.any(pos_pairs, dim=1)  # 哪些物品有正样本对

        if not pos_items.any():
            return torch.tensor(0.0, device=projected_features.device, requires_grad=True)

        # 矩阵化 InfoNCE 损失计算
        # 1. 计算指数化的分数矩阵
        exp_score_matrix = torch.exp(score_matrix)  # [batch_size, batch_size]

        # 2. 计算每个物品的正样本分数和
        pos_scores = exp_score_matrix * pos_pairs.float()  # 只保留正样本位置的分数
        pos_sums = pos_scores.sum(dim=1)  # [batch_size] - 每个物品的正样本分数和

        # 3. 构建负样本掩码：排除自己和正样本
        diag_mask = torch.eye(batch_size, device=score_matrix.device).bool()
        neg_mask = ~(diag_mask | pos_pairs)  # [batch_size, batch_size] - 负样本掩码

        # 4. 计算每个物品的负样本分数和
        neg_scores = exp_score_matrix * neg_mask.float()  # 只保留负样本位置的分数
        neg_sums = neg_scores.sum(dim=1)  # [batch_size] - 每个物品的负样本分数和

        # 5. 检查每个物品是否有有效的负样本
        has_negatives = neg_mask.any(dim=1)  # [batch_size]

        # 6. 只对有正样本且有负样本的物品计算损失
        valid_items = pos_items & has_negatives  # [batch_size]

        if not valid_items.any():
            return torch.tensor(0.0, device=projected_features.device, requires_grad=True)

        # 7. 提取有效物品的正样本和负样本分数和
        valid_pos_sums = pos_sums[valid_items]  # [valid_count]
        valid_neg_sums = neg_sums[valid_items]  # [valid_count]

        # 8. 矩阵化 InfoNCE 损失计算
        losses = -torch.log(valid_pos_sums / (valid_pos_sums + valid_neg_sums + 1e-8))  # [valid_count]

        # 9. 返回平均损失
        return losses.mean()

    def build_batch_similarity_matrix(self, edges, batch_indices, total_items):
        """构建批次内的相似度矩阵"""
        batch_size = len(batch_indices)
        similarity_matrix = torch.zeros(batch_size, batch_size, device=batch_indices.device)

        # 创建索引映射：从全局索引到批次内索引
        index_map = {item.item(): i for i, item in enumerate(batch_indices)}

        # 遍历所有边，找到在当前批次内的边
        for i in range(len(edges[0])):
            node_i, node_j = edges[0][i].item(), edges[1][i].item()
            if node_i in index_map and node_j in index_map:
                batch_i, batch_j = index_map[node_i], index_map[node_j]
                similarity_matrix[batch_i, batch_j] = 1
                similarity_matrix[batch_j, batch_i] = 1  # 对称矩阵

        return similarity_matrix

    def build_batch_edge_matrix(self, edges, batch_indices, total_items):
        """构建批次内的连边矩阵（0/1矩阵）"""
        batch_size = len(batch_indices)
        edge_matrix = torch.zeros(batch_size, batch_size, device=batch_indices.device)

        # 创建索引映射：从全局索引到批次内索引
        index_map = {item.item(): i for i, item in enumerate(batch_indices)}

        # 遍历边，构建批次内的连边矩阵
        for i in range(len(edges[0])):
            src = edges[0][i].item()
            dst = edges[1][i].item()

            # 检查边的两个端点是否都在当前批次中
            if src in index_map and dst in index_map:
                src_idx = index_map[src]
                dst_idx = index_map[dst]
                edge_matrix[src_idx, dst_idx] = 1
                edge_matrix[dst_idx, src_idx] = 1  # 无向图

        return edge_matrix

    def get_fused_item_embeddings(self, lightgcn_items, item_indices, domain="source"):
        """
        融合LightGCN、结构和语义嵌入得到最终的物品表示

        参数:
        - lightgcn_items: LightGCN生成的物品嵌入
        - item_indices: 物品索引
        - domain: 域类型 ("source" 或 "target")

        返回:
        - 融合后的物品嵌入
        """
        if self.node2vec_embeddings is None or self.word2vec_embeddings is None:
            # 如果没有预训练嵌入，直接返回LightGCN嵌入
            return lightgcn_items[item_indices]

        # 获取对应的LightGCN嵌入
        lightgcn_emb = lightgcn_items[item_indices]  # [batch_size, feature_dim]

        # 计算预训练嵌入的全局索引
        if domain == "target":
            # 目标域物品需要加上源域物品数量的偏移
            global_item_indices = item_indices + self.opt["source_item_num"]
        else:
            # 源域物品直接使用原索引
            global_item_indices = item_indices

        # 确保索引在预训练嵌入范围内
        max_pretrained_idx = min(self.node2vec_embeddings.size(0), self.word2vec_embeddings.size(0)) - 1
        global_item_indices = torch.clamp(global_item_indices, 0, max_pretrained_idx)

        # 获取对应的结构和语义嵌入
        structural_emb = self.node2vec_embeddings[global_item_indices]  # [batch_size, feature_dim]
        semantic_emb = self.word2vec_embeddings[global_item_indices]    # [batch_size, feature_dim]

        # 拼接三种嵌入
        fused_input = torch.cat([lightgcn_emb, structural_emb, semantic_emb], dim=1)  # [batch_size, feature_dim*3]

        # 通过融合层得到最终表示
        fused_emb = self.item_fusion(fused_input)  # [batch_size, feature_dim]

        return fused_emb

    def get_item_batch_indices(self, total_items, training_batch=None):
        """
        获取物品对比学习的batch索引 - 内存安全版本
        限制批次大小以避免内存溢出
        """
        device = next(self.parameters()).device

        if training_batch is not None:
            # 使用训练批次中涉及的物品索引
            batch_indices = self.get_training_batch_indices(training_batch, total_items)
            # 如果批次太大，随机采样
            if len(batch_indices) > self.max_contrastive_batch_size:
                perm = torch.randperm(len(batch_indices))[:self.max_contrastive_batch_size]
                batch_indices = batch_indices[perm]
            return batch_indices
        else:
            # 如果没有训练批次，随机采样物品
            sample_size = min(total_items, self.max_contrastive_batch_size)
            return torch.randperm(total_items)[:sample_size].to(device)




    def get_training_batch_indices(self, training_batch, total_items):
        """
        从训练batch中提取所有涉及的物品索引 - GPU优化版本，避免CPU-GPU数据传输
        """
        device = next(self.parameters()).device

        if training_batch is None:
            return torch.arange(total_items, device=device)

        try:
            # 收集所有物品张量（确保设备一致性）
            batch_item_tensors = []

            # 源域物品（正样本和负样本）
            if len(training_batch) > 1:
                source_pos_items = training_batch[1].flatten()
                # 确保张量在正确的设备上
                if source_pos_items.device != device:
                    source_pos_items = source_pos_items.to(device)
                batch_item_tensors.append(source_pos_items)

            if len(training_batch) > 2:
                source_neg_items = training_batch[2].flatten()
                # 确保张量在正确的设备上
                if source_neg_items.device != device:
                    source_neg_items = source_neg_items.to(device)
                batch_item_tensors.append(source_neg_items)

            # 目标域物品（需要加上偏移量）
            if len(training_batch) > 4:
                target_pos_items = training_batch[4].flatten()
                # 确保张量在正确的设备上
                if target_pos_items.device != device:
                    target_pos_items = target_pos_items.to(device)
                target_pos_global = target_pos_items + self.opt["source_item_num"]
                batch_item_tensors.append(target_pos_global)

            if len(training_batch) > 5:
                target_neg_items = training_batch[5].flatten()
                # 确保张量在正确的设备上
                if target_neg_items.device != device:
                    target_neg_items = target_neg_items.to(device)
                target_neg_global = target_neg_items + self.opt["source_item_num"]
                batch_item_tensors.append(target_neg_global)

            if not batch_item_tensors:
                return torch.arange(total_items, device=device)

            # 合并所有物品索引（保持在GPU上）
            all_batch_items = torch.cat(batch_item_tensors)

            # 过滤有效索引（使用张量操作）
            valid_mask = (all_batch_items >= 0) & (all_batch_items < total_items)
            valid_items = all_batch_items[valid_mask]

            # 去重（保持在GPU上）
            unique_items = torch.unique(valid_items)

            if unique_items.numel() == 0:
                return torch.arange(total_items, device=device)

            return unique_items

        except Exception as e:
            print(f"提取训练batch物品时出错: {e}")
            return torch.arange(total_items, device=device)

    def get_batch_overlap_users(self, training_batch, overlap_indices):
        """
        从训练批次中提取重叠用户 - GPU优化版本，避免CPU-GPU数据传输
        """
        if training_batch is None:
            return overlap_indices

        device = overlap_indices.device

        try:
            # 收集批次中的所有用户（确保设备一致性）
            batch_user_tensors = []

            # 提取源域用户
            if len(training_batch) > 0:
                source_users = training_batch[0].flatten()
                # 确保张量在正确的设备上
                if source_users.device != device:
                    source_users = source_users.to(device)
                batch_user_tensors.append(source_users)

            # 提取目标域用户
            if len(training_batch) > 3:
                target_users = training_batch[3].flatten()
                # 确保张量在正确的设备上
                if target_users.device != device:
                    target_users = target_users.to(device)
                batch_user_tensors.append(target_users)

            if not batch_user_tensors:
                return torch.empty(0, dtype=torch.long, device=device)

            # 合并所有批次用户（现在所有张量都在同一设备上）
            all_batch_users = torch.cat(batch_user_tensors)
            unique_batch_users = torch.unique(all_batch_users)

            # 确保两个张量都在同一设备上
            if unique_batch_users.device != device:
                unique_batch_users = unique_batch_users.to(device)

            # 使用张量操作找到重叠用户（避免CPU转换）
            # 创建掩码：检查每个重叠用户是否在批次中
            overlap_mask = torch.isin(overlap_indices, unique_batch_users)
            batch_overlap_users = overlap_indices[overlap_mask]

            return batch_overlap_users

        except Exception as e:
            print(f"提取训练batch用户时出错: {e}")
            print(f"设备信息: overlap_indices.device={overlap_indices.device}")
            if len(training_batch) > 0:
                print(f"training_batch[0].device={training_batch[0].device}")
            if len(training_batch) > 3:
                print(f"training_batch[3].device={training_batch[3].device}")
            return overlap_indices




    def forward(self, source_UV, source_VU, target_UV, target_VU, training_batch=None):
        # 直接使用LightGCN进行用户-物品交互学习
        source_learn_user, source_learn_item = self.source_lightgcn(source_UV)
        target_learn_user, target_learn_item = self.target_lightgcn(target_UV)

        # 使用基于图连边的对比学习（直接使用预训练嵌入）
        # 获取物品对比学习的batch索引（基于预训练嵌入的总物品数）
        if self.node2vec_embeddings is not None:
            total_items = self.node2vec_embeddings.size(0)
        elif self.word2vec_embeddings is not None:
            total_items = self.word2vec_embeddings.size(0)
        else:
            # 如果没有预训练嵌入，使用LightGCN物品数作为fallback
            total_items = source_learn_item.size(0) + target_learn_item.size(0)

        item_batch_indices = self.get_item_batch_indices(total_items, training_batch=training_batch)

        # 域间对比学习：结构相似物品对要求对应语义表示为正样本对
        # 结构对比学习：使用结构相似图的连边作为正样本对，但在语义表示空间（Word2Vec）中计算损失
        struc_start = time.time()
        self.item_struc_contrastive_loss = self.graph_based_contrastive_loss(
            graph_type="structural", batch_indices=item_batch_indices
        )
        self._struc_loss_time = time.time() - struc_start

        # 语义对比学习：使用语义相似图的连边作为正样本对，但在结构表示空间（Node2Vec）中计算损失
        seman_start = time.time()
        self.item_seman_contrastive_loss = self.graph_based_contrastive_loss(
            graph_type="semantic", batch_indices=item_batch_indices
        )
        self._seman_loss_time = time.time() - seman_start

        # 跨模态对比学习：让同一物品的结构嵌入和语义嵌入更相似（优化后版本）
        cross_modal_start = time.time()
        self.cross_modal_loss = self.cross_modal_contrastive_loss(
            item_batch_indices, batch_indices=item_batch_indices
        )
        self._cross_modal_loss_time = time.time() - cross_modal_start


        # 使用对比学习替代对抗学习处理重叠用户
        overlap_start = time.time()
        self.overlap_user_loss = self.overlap_user_contrastive_loss(
            source_learn_user, target_learn_user, self.overlap_user_index, training_batch
        )
        self._overlap_user_loss_time = time.time() - overlap_start

        # 移除表示交换机制，直接使用原始用户表示
        source_learn_user_new = source_learn_user
        target_learn_user_new = target_learn_user

        # 非重叠用户索引（GPU优化版本，避免CPU-GPU数据传输）
        device = self.overlap_user_index.device
        source_all_idx = torch.arange(self.opt["source_user_num"], device=device)
        target_all_idx = torch.arange(self.opt["target_user_num"], device=device)

        # 使用张量操作找到非重叠用户（避免CPU转换）
        source_overlap_mask = torch.isin(source_all_idx, self.overlap_user_index)
        target_overlap_mask = torch.isin(target_all_idx, self.overlap_user_index)

        source_non_overlap_idx = source_all_idx[~source_overlap_mask]
        target_non_overlap_idx = target_all_idx[~target_overlap_mask]


        # 使用对比学习后，不需要交换用户表示，直接使用原始表示
        source_learn_user_concat = torch.cat((
            source_learn_user_new[self.overlap_user_index],  # 重叠用户（来自source）
            source_learn_user_new[source_non_overlap_idx]  # 非重叠用户（来自source）
        ), dim=0)

        target_learn_user_concat = torch.cat((
            target_learn_user_new[self.overlap_user_index],  # 重叠用户（来自target）
            target_learn_user_new[target_non_overlap_idx]  # 非重叠用户（来自target）
        ), dim=0)


        return source_learn_user_concat, source_learn_item, target_learn_user_concat, target_learn_item