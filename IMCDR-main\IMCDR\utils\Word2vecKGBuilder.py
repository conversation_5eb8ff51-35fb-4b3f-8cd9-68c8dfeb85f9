
import os
import time
import pickle
from collections import defaultdict

import numpy as np
import torch
import torch.nn.functional as F
from tqdm import tqdm
from gensim.models import Word2Vec
from sklearn.metrics.pairwise import cosine_similarity
import scipy.sparse as sp
import matplotlib.pyplot as plt


class Word2vecKGBuilder:
    def __init__(self, opt):
        self.opt = opt
        self.source_item_num = opt.get("source_item_num", 0)
        self.target_item_num = opt.get("target_item_num", 0)
        self.feature_dim = opt.get("feature_dim", 128)
        self.similarity_threshold = opt.get("seman_similarity_threshold", 0.5)

        # 使用与node2vec相同的嵌入目录配置
        self.embedding_dir = opt.get("embedding_dir", "./embeddings")
        self.dataset_name = opt.get("dataset", "default")

        self.epochs = opt.get("epochs", 20)

        if not os.path.exists(self.embedding_dir):
            os.makedirs(self.embedding_dir)

        self.source_kg_triplets = []
        self.target_kg_triplets = []
        self.source_entity_dict = {}
        self.target_entity_dict = {}
        self.common_entities = set()

        self.corpus = []
        self.model = None
        self.source_embeddings = {}
        self.target_embeddings = {}
        self.similar_item_pairs = []
        self.cross_domain_item_graph = None

    def load_kg_data(self, source_kg_path, target_kg_path):
        def load_file(path):
            triples = []
            import pandas as pd
            df = pd.read_csv(path)
            for _, row in df.iterrows():
                h, r, t = int(row['item_id']), int(row['relation']), int(row['entity'])
                triples.append((h, r, t))
            return triples

        self.source_kg_triplets = load_file(source_kg_path)
        self.target_kg_triplets = load_file(target_kg_path)
        print(f"加载了 {len(self.source_kg_triplets)} 个源域三元组和 {len(self.target_kg_triplets)} 个目标域三元组")

    def load_entity_dictionaries(self, source_entity_dict_path, target_entity_dict_path):
        """
        加载统一的实体字典，并根据各域三元组确定实际使用的实体和共有实体
        注意：源域和目标域共享同一个实体字典文件，但各域只使用其中的部分实体
        """
        def load_dict(path):
            entity_map = {}
            import pandas as pd
            df = pd.read_csv(path)
            for _, row in df.iterrows():
                entity_id = int(row['entity_id'])
                entity_name = row['entity']
                entity_map[entity_id] = entity_name
            return entity_map

        print(f"加载统一实体字典: {source_entity_dict_path}")

        # 在新格式中，源域和目标域共享同一个实体字典
        unified_entity_dict = load_dict(source_entity_dict_path)
        self.source_entity_dict = unified_entity_dict.copy()
        self.target_entity_dict = unified_entity_dict.copy()

        # 根据三元组数据确定各域实际使用的实体和真正的共有实体
        self._identify_actual_entities_and_common()

        print(f"统一实体字典加载完成:")
        print(f"  - 总实体数: {len(unified_entity_dict)}")
        print(f"  - 源域实际使用实体数: {len(self.source_actual_entities)}")
        print(f"  - 目标域实际使用实体数: {len(self.target_actual_entities)}")
        print(f"  - 真正的共有实体数: {len(self.common_entities)}")

    def _identify_actual_entities_and_common(self):
        """
        根据三元组数据识别各域实际使用的实体和真正的共有实体
        """
        if self.source_kg_triplets is None or self.target_kg_triplets is None:
            print("警告：请先加载知识图谱数据")
            self.source_actual_entities = set()
            self.target_actual_entities = set()
            self.common_entities = set()
            return

        # 从源域三元组中提取实际使用的实体
        source_entity_ids = set()
        for triplet in self.source_kg_triplets:
            source_entity_ids.add(triplet[2])  # 尾实体ID

        self.source_actual_entities = set()
        for entity_id in source_entity_ids:
            if entity_id in self.source_entity_dict:
                self.source_actual_entities.add(self.source_entity_dict[entity_id])

        # 从目标域三元组中提取实际使用的实体
        target_entity_ids = set()
        for triplet in self.target_kg_triplets:
            target_entity_ids.add(triplet[2])  # 尾实体ID

        self.target_actual_entities = set()
        for entity_id in target_entity_ids:
            if entity_id in self.target_entity_dict:
                self.target_actual_entities.add(self.target_entity_dict[entity_id])

        # 计算真正的共有实体（在两个域的三元组中都实际出现的实体）
        self.common_entities = self.source_actual_entities & self.target_actual_entities

        print(f"实体使用情况分析:")
        print(f"  - 源域独有实体: {len(self.source_actual_entities - self.common_entities)}")
        print(f"  - 目标域独有实体: {len(self.target_actual_entities - self.common_entities)}")
        print(f"  - 真正共有实体: {len(self.common_entities)}")

        # 打印一些共有实体的例子（如果有的话）
        if self.common_entities:
            sample_common = list(self.common_entities)[:5]
            print(f"  - 共有实体示例: {sample_common}")
        else:
            print("  - 警告：没有发现共有实体！")

    def build_item_entity_corpus(self):
        item_context = defaultdict(set)

        for h, r, t in self.source_kg_triplets:
            if t in self.source_entity_dict:
                item_context[f"s_item_{h}"].add(self.source_entity_dict[t])

        for h, r, t in self.target_kg_triplets:
            if t in self.target_entity_dict:
                item_context[f"t_item_{h}"].add(self.target_entity_dict[t])

        for item, context in item_context.items():
            self.corpus.append([item] + list(context))

        print(f"构建了 {len(self.corpus)} 条 item-entity 共现语料")

    def train_word2vec_embeddings(self):
        print("训练 Word2Vec 模型中...")
        # self.model = Word2Vec(
        #     sentences=self.corpus,
        #     vector_size=self.feature_dim,
        #     window=5,
        #     min_count=1,
        #     sg=1,
        #     workers=4
        # )
        self.model = Word2Vec(
            vector_size=self.feature_dim,
            window=5,
            min_count=1,
            sg=1,
            workers=4
        )
        self.model.build_vocab(self.corpus)
        self.model.train(self.corpus, total_examples=len(self.corpus), epochs=self.epochs)

        print("Word2Vec 训练完成。")

        for word in self.model.wv.index_to_key:
            if word.startswith("s_item_"):
                item_id = int(word.split("_")[2])
                self.source_embeddings[item_id] = self.model.wv[word]
            elif word.startswith("t_item_"):
                item_id = int(word.split("_")[2])
                self.target_embeddings[item_id] = self.model.wv[word]

        print(f"提取了 {len(self.source_embeddings)} 个源域和 {len(self.target_embeddings)} 个目标域物品的向量")

    def get_embedding_file_path(self, dimensions=None):
        """
        获取嵌入文件路径（与node2vec保持一致）

        参数:
        - dimensions: 嵌入维度

        返回:
        - embedding_file_path: 嵌入文件路径
        """
        if dimensions is None:
            dimensions = self.feature_dim

        # 使用与node2vec相同的命名格式
        file_name = f"{self.dataset_name}_merged_kg_word2vec_d{dimensions}.pkl"
        return os.path.join(self.embedding_dir, file_name)

    def save_word2vec_embeddings(self):
        """
        保存Word2Vec嵌入到文件（与node2vec格式保持一致）
        """
        import os
        import pickle
        import time

        # 使用与node2vec相同的保存格式
        embedding_file = self.get_embedding_file_path()

        # 创建保存目录
        os.makedirs(os.path.dirname(embedding_file), exist_ok=True)

        # 保存嵌入和相关信息（与node2vec格式一致）
        data_to_save = {
            'node_embeddings': {},  # 为了兼容性保留，但为空
            'source_embeddings': self.source_embeddings,
            'target_embeddings': self.target_embeddings,
            'dimensions': self.feature_dim,
            'timestamp': time.time()
        }

        try:
            with open(embedding_file, 'wb') as f:
                pickle.dump(data_to_save, f)
            print(f"Word2Vec嵌入已保存到: {embedding_file}")
            return True
        except Exception as e:
            print(f"保存Word2Vec嵌入出错: {e}")
            return False

    def load_word2vec_embeddings(self):
        """
        从文件加载Word2Vec嵌入（与node2vec格式保持一致）

        返回:
        - 是否成功加载
        """
        import os
        import pickle
        import time

        embedding_file = self.get_embedding_file_path()

        if not os.path.exists(embedding_file):
            print(f"Word2Vec嵌入文件不存在: {embedding_file}")
            return False

        try:
            with open(embedding_file, 'rb') as f:
                data = pickle.load(f)

            # 提取嵌入和相关信息（与node2vec格式一致）
            self.source_embeddings = data['source_embeddings']
            self.target_embeddings = data['target_embeddings']

            # 检查维度是否匹配
            loaded_dimensions = data['dimensions']
            if loaded_dimensions != self.feature_dim:
                print(f"警告: 加载的嵌入维度 ({loaded_dimensions}) 与请求的维度 ({self.feature_dim}) 不匹配")

            # 显示嵌入文件的创建时间
            timestamp = data.get('timestamp', 'unknown')
            if timestamp != 'unknown':
                time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
                print(f"加载了创建于 {time_str} 的Word2Vec嵌入文件")

            print(f"成功从 {embedding_file} 加载Word2Vec嵌入")
            print(f"加载了 {len(self.source_embeddings)} 个源域物品和 {len(self.target_embeddings)} 个目标域物品的嵌入")

            return True
        except Exception as e:
            print(f"加载Word2Vec嵌入时出错: {e}")
            return False

    def _apply_topk_filter_cross_domain(self, similarity_matrix, source_ids, target_ids, threshold, top_k):
        """
        为跨域相似度应用Top-k过滤

        参数:
        - similarity_matrix: 跨域相似度矩阵 [source_items, target_items]
        - source_ids: 源域物品ID列表
        - target_ids: 目标域物品ID列表
        - threshold: 相似度阈值
        - top_k: 每个源域物品保留的最相似目标域物品数量

        返回:
        - similar_pairs: 过滤后的相似物品对列表
        """
        similar_pairs = []

        for i, source_id in enumerate(source_ids):
            # 获取当前源域物品与所有目标域物品的相似度
            similarities = similarity_matrix[i, :]

            # 找到超过阈值的目标域物品
            valid_indices = np.where(similarities >= threshold)[0]

            if len(valid_indices) > 0:
                # 获取有效的相似度值
                valid_similarities = similarities[valid_indices]

                # 按相似度降序排序，取Top-k
                sorted_indices = np.argsort(valid_similarities)[::-1][:top_k]
                top_indices = valid_indices[sorted_indices]

                # 添加到结果列表
                for j in top_indices:
                    target_id = target_ids[j]
                    similarity = similarities[j]
                    similar_pairs.append((source_id, target_id, similarity))

        return similar_pairs

    def _extract_intra_domain_pairs_word2vec(self, item_ids, similarity_matrix, threshold, domain_name, top_k=None):
        """
        从域内相似度矩阵中提取相似物品对（Word2Vec版本）

        参数:
        - item_ids: 物品ID列表
        - similarity_matrix: 相似度矩阵
        - threshold: 相似度阈值
        - domain_name: 域名称（用于日志）
        - top_k: 每个物品保留的最相似物品数量，None表示不限制

        返回:
        - similar_pairs: 相似物品对列表 [(item1_id, item2_id, similarity), ...]
        """
        print(f"筛选{domain_name}内相似物品对...")
        start_filter_time = time.time()

        if top_k is not None:
            # Top-k过滤：为每个物品保留最相似的k个物品
            print(f"应用Top-{top_k}过滤...")
            similar_pairs = self._apply_topk_filter_intra_domain(
                similarity_matrix, item_ids, threshold, top_k
            )
        else:
            # 传统阈值过滤
            # 创建上三角掩码，避免重复和自相似
            n_items = len(item_ids)
            upper_tri_mask = np.triu(np.ones((n_items, n_items)), k=1).astype(bool)

            # 结合阈值条件和上三角掩码
            valid_mask = (similarity_matrix >= threshold) & upper_tri_mask

            # 使用向量化操作找到所有满足条件的位置
            similar_indices = np.where(valid_mask)

            # 优化：使用列表推导式
            if len(similar_indices[0]) > 0:
                item_ids_array = np.array(item_ids)
                similar_pairs = [
                    (item_ids_array[i], item_ids_array[j], similarity_matrix[i, j])
                    for i, j in zip(similar_indices[0], similar_indices[1])
                ]
            else:
                similar_pairs = []

        print(f"{domain_name}内筛选耗时: {time.time() - start_filter_time:.2f}秒")
        print(f"找到 {len(similar_pairs)} 对{domain_name}内相似物品 (相似度 > {threshold})")
        return similar_pairs

    def _apply_topk_filter_intra_domain(self, similarity_matrix, item_ids, threshold, top_k):
        """
        为域内相似度应用Top-k过滤

        参数:
        - similarity_matrix: 域内相似度矩阵 [items, items]
        - item_ids: 物品ID列表
        - threshold: 相似度阈值
        - top_k: 每个物品保留的最相似物品数量

        返回:
        - similar_pairs: 过滤后的相似物品对列表
        """
        similar_pairs = []
        item_ids_array = np.array(item_ids)
        n_items = len(item_ids)

        for i in range(n_items):
            # 获取当前物品与其他物品的相似度
            similarities = similarity_matrix[i, :]

            # 排除自己，找到超过阈值的物品
            valid_indices = []
            for j in range(n_items):
                if i != j and similarities[j] >= threshold:
                    valid_indices.append(j)

            if len(valid_indices) > 0:
                # 获取有效的相似度值
                valid_similarities = similarities[valid_indices]

                # 按相似度降序排序，取Top-k
                sorted_indices = np.argsort(valid_similarities)[::-1][:top_k]
                top_indices = [valid_indices[idx] for idx in sorted_indices]

                # 添加到结果列表（避免重复，只添加i < j的对）
                for j in top_indices:
                    if i < j:  # 避免重复添加
                        item1_id = item_ids_array[i]
                        item2_id = item_ids_array[j]
                        similarity = similarities[j]
                        similar_pairs.append((item1_id, item2_id, similarity))

        return similar_pairs

    def gpu_cosine_similarity(self, embeddings1, embeddings2, batch_size=1000):
        """
        GPU加速的余弦相似度计算

        参数:
        - embeddings1: 第一组嵌入 (numpy array)
        - embeddings2: 第二组嵌入 (numpy array)
        - batch_size: 批处理大小，避免GPU内存溢出

        返回:
        - similarity_matrix: 相似度矩阵 (numpy array)
        """
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 转换为GPU tensor
        emb1 = torch.tensor(embeddings1, dtype=torch.float32).to(device)
        emb2 = torch.tensor(embeddings2, dtype=torch.float32).to(device)

        # L2归一化
        emb1_norm = F.normalize(emb1, p=2, dim=1)
        emb2_norm = F.normalize(emb2, p=2, dim=1)

        n1, n2 = emb1_norm.size(0), emb2_norm.size(0)

        # 分批计算，避免内存溢出
        similarity_matrix = torch.zeros(n1, n2, device=device)

        for i in range(0, n1, batch_size):
            end_i = min(i + batch_size, n1)
            batch_emb1 = emb1_norm[i:end_i]

            for j in range(0, n2, batch_size):
                end_j = min(j + batch_size, n2)
                batch_emb2 = emb2_norm[j:end_j]

                # 计算批次相似度
                batch_sim = torch.mm(batch_emb1, batch_emb2.t())
                similarity_matrix[i:end_i, j:end_j] = batch_sim

        return similarity_matrix.cpu().numpy()

    def calculate_item_similarity(self, threshold=0.5, top_k=None):
        """
        计算所有物品之间的相似度，统一处理不区分跨域和域内

        参数:
        - threshold: 相似度阈值
        - top_k: 每个物品保留的最相似物品数量，None表示不限制

        返回:
        - similar_item_pairs: 所有相似物品对列表 [(item1_global_id, item2_global_id, similarity), ...]
        """
        if self.source_embeddings is None or self.target_embeddings is None:
            print("错误：请先生成Word2Vec嵌入")
            return []

        print("计算所有物品相似度（统一处理）...")

        # 合并所有物品嵌入，构建全局物品矩阵
        all_item_ids = []
        all_embeddings = []

        # 获取源域物品数量
        source_item_num = max(self.source_embeddings.keys()) + 1 if self.source_embeddings else 0

        # 添加源域物品（全局ID = 原ID）
        for item_id in sorted(self.source_embeddings.keys()):
            all_item_ids.append(item_id)  # 源域物品保持原ID
            all_embeddings.append(self.source_embeddings[item_id])

        # 添加目标域物品（全局ID = 原ID + source_item_num）
        for item_id in sorted(self.target_embeddings.keys()):
            global_id = item_id + source_item_num  # 目标域物品加偏移
            all_item_ids.append(global_id)
            all_embeddings.append(self.target_embeddings[item_id])

        all_embeddings = np.array(all_embeddings)
        total_items = len(all_item_ids)

        print(f"总物品数: {total_items} (源域: {len(self.source_embeddings)}, 目标域: {len(self.target_embeddings)})")

        # 计算全局相似度矩阵
        print("计算全局物品相似度矩阵...")
        start_time = time.time()

        gpu_batch_size = 1000  # 可以根据GPU内存调整
        if torch.cuda.is_available():
            print(f"使用GPU加速计算相似度 (批处理大小: {gpu_batch_size})...")
            similarity_matrix = self.gpu_cosine_similarity(all_embeddings, all_embeddings, gpu_batch_size)
        else:
            print("使用CPU计算相似度...")
            similarity_matrix = cosine_similarity(all_embeddings, all_embeddings)

        print(f"相似度计算耗时: {time.time() - start_time:.2f}秒")

        # 提取相似物品对
        print("提取相似物品对...")
        start_filter_time = time.time()

        if top_k is not None:
            # Top-k过滤：为每个物品保留最相似的k个物品
            print(f"应用Top-{top_k}过滤...")

            # 根据数据规模选择最优算法
            if total_items > 5000:
                print("使用稀疏矩阵优化算法...")
                similar_pairs = self._apply_topk_filter_sparse(
                    similarity_matrix, all_item_ids, threshold, top_k
                )
            else:
                print("使用向量化算法...")
                similar_pairs = self._apply_topk_filter_unified(
                    similarity_matrix, all_item_ids, threshold, top_k
                )
        else:
            # 传统阈值过滤
            similar_pairs = self._apply_threshold_filter_unified(
                similarity_matrix, all_item_ids, threshold
            )

        print(f"筛选耗时: {time.time() - start_filter_time:.2f}秒")
        print(f"找到 {len(similar_pairs)} 对相似物品 (相似度 > {threshold})")

        # 保存结果
        self.similar_item_pairs = similar_pairs

        return similar_pairs

    def _apply_threshold_filter_unified(self, similarity_matrix, all_item_ids, threshold):
        """
        统一的阈值过滤方法

        参数:
        - similarity_matrix: 全局相似度矩阵 [all_items, all_items]
        - all_item_ids: 所有物品的全局ID列表
        - threshold: 相似度阈值

        返回:
        - similar_pairs: 过滤后的相似物品对列表
        """
        similar_pairs = []
        n_items = len(all_item_ids)

        # 创建上三角掩码，避免重复和自相似
        upper_tri_mask = np.triu(np.ones((n_items, n_items)), k=1).astype(bool)

        # 结合阈值条件和上三角掩码
        valid_mask = (similarity_matrix >= threshold) & upper_tri_mask

        # 使用向量化操作找到所有满足条件的位置
        similar_indices = np.where(valid_mask)

        if len(similar_indices[0]) > 0:
            all_item_ids_array = np.array(all_item_ids)
            similar_pairs = [
                (all_item_ids_array[i], all_item_ids_array[j], similarity_matrix[i, j])
                for i, j in zip(similar_indices[0], similar_indices[1])
            ]

        return similar_pairs

    def _apply_topk_filter_unified(self, similarity_matrix, all_item_ids, threshold, top_k):
        """
        向量化优化的Top-k过滤方法

        参数:
        - similarity_matrix: 全局相似度矩阵 [all_items, all_items]
        - all_item_ids: 所有物品的全局ID列表
        - threshold: 相似度阈值
        - top_k: 每个物品保留的最相似物品数量

        返回:
        - similar_pairs: 过滤后的相似物品对列表
        """
        print(f"开始向量化Top-{top_k}过滤...")
        start_time = time.time()

        n_items = len(all_item_ids)
        all_item_ids_array = np.array(all_item_ids)
        similar_pairs = []

        # 1. 向量化创建阈值掩码和对角线掩码
        print("创建掩码矩阵...")
        threshold_mask = similarity_matrix >= threshold  # 阈值掩码
        diag_mask = np.eye(n_items, dtype=bool)          # 对角线掩码
        valid_mask = threshold_mask & (~diag_mask)       # 有效位置掩码

        print(f"有效相似对总数: {np.sum(valid_mask)}")

        # 2. 高效批量处理Top-k选择
        print("执行向量化Top-k选择...")

        # 预分配结果列表以提高效率
        all_i_indices = []
        all_j_indices = []
        all_similarities = []

        # 分批处理以减少内存使用
        batch_size = min(1000, n_items)

        for batch_start in range(0, n_items, batch_size):
            batch_end = min(batch_start + batch_size, n_items)
            batch_indices = np.arange(batch_start, batch_end)

            # 批量获取当前批次的有效掩码
            batch_valid_mask = valid_mask[batch_start:batch_end, :]

            for local_i, global_i in enumerate(batch_indices):
                # 向量化获取当前行的有效邻居
                valid_indices = np.where(batch_valid_mask[local_i, :])[0]

                if len(valid_indices) > 0:
                    # 获取有效相似度值
                    valid_similarities = similarity_matrix[global_i, valid_indices]

                    # 优化的Top-k选择
                    if len(valid_indices) <= top_k:
                        # 如果有效邻居数 <= top_k，全部保留
                        selected_indices = valid_indices
                        selected_similarities = valid_similarities
                    else:
                        # 使用argpartition进行部分排序（比完全排序快）
                        partition_indices = np.argpartition(valid_similarities, -top_k)[-top_k:]
                        # 对top-k部分进行完全排序
                        sorted_partition = partition_indices[np.argsort(valid_similarities[partition_indices])[::-1]]
                        selected_indices = valid_indices[sorted_partition]
                        selected_similarities = valid_similarities[sorted_partition]

                    # 向量化过滤 i < j 的条件
                    mask_i_less_j = selected_indices > global_i
                    if np.any(mask_i_less_j):
                        filtered_j_indices = selected_indices[mask_i_less_j]
                        filtered_similarities = selected_similarities[mask_i_less_j]

                        # 批量添加到预分配的列表
                        n_pairs = len(filtered_j_indices)
                        all_i_indices.extend([global_i] * n_pairs)
                        all_j_indices.extend(filtered_j_indices)
                        all_similarities.extend(filtered_similarities)

            if (batch_start // batch_size + 1) % 5 == 0:
                print(f"已处理 {batch_end}/{n_items} 个物品...")

        # 3. 向量化构建最终结果
        print("构建最终结果...")
        if len(all_i_indices) > 0:
            # 转换为numpy数组进行向量化操作
            i_array = np.array(all_i_indices)
            j_array = np.array(all_j_indices)
            sim_array = np.array(all_similarities)

            # 向量化获取物品ID
            item_i_ids = all_item_ids_array[i_array]
            item_j_ids = all_item_ids_array[j_array]

            # 批量构建结果元组
            similar_pairs = list(zip(item_i_ids, item_j_ids, sim_array))

        elapsed_time = time.time() - start_time
        print(f"向量化Top-k过滤完成，耗时: {elapsed_time:.2f}秒")
        print(f"最终保留 {len(similar_pairs)} 对相似物品")

        return similar_pairs

    def _apply_topk_filter_sparse(self, similarity_matrix, all_item_ids, threshold, top_k):
        """
        使用稀疏矩阵的高效Top-k过滤方法（适用于大规模数据）

        参数:
        - similarity_matrix: 全局相似度矩阵 [all_items, all_items]
        - all_item_ids: 所有物品的全局ID列表
        - threshold: 相似度阈值
        - top_k: 每个物品保留的最相似物品数量

        返回:
        - similar_pairs: 过滤后的相似物品对列表
        """
        import scipy.sparse as sp

        print(f"开始稀疏矩阵Top-{top_k}过滤...")
        start_time = time.time()

        n_items = len(all_item_ids)
        all_item_ids_array = np.array(all_item_ids)

        # 1. 创建阈值掩码并转换为稀疏矩阵
        print("创建稀疏阈值矩阵...")
        threshold_mask = similarity_matrix >= threshold
        np.fill_diagonal(threshold_mask, False)  # 排除对角线

        # 转换为稀疏矩阵以节省内存
        sparse_sim = sp.csr_matrix(similarity_matrix * threshold_mask)
        print(f"稀疏度: {1 - sparse_sim.nnz / (n_items * n_items):.4f}")

        # 2. 高效处理每行的Top-k选择
        print("执行稀疏Top-k选择...")
        similar_pairs = []

        for i in range(n_items):
            # 获取第i行的稀疏数据
            row = sparse_sim.getrow(i)
            if row.nnz > 0:  # 如果该行有非零元素
                # 获取非零元素的列索引和值
                _, cols = row.nonzero()
                data = row.data

                if len(cols) <= top_k:
                    # 如果非零元素数 <= top_k，全部保留
                    selected_cols = cols
                    selected_similarities = data
                else:
                    # 使用argpartition选择top-k
                    top_indices = np.argpartition(data, -top_k)[-top_k:]
                    sorted_top = top_indices[np.argsort(data[top_indices])[::-1]]
                    selected_cols = cols[sorted_top]
                    selected_similarities = data[sorted_top]

                # 只添加 i < j 的对
                for j, sim in zip(selected_cols, selected_similarities):
                    if i < j:
                        similar_pairs.append((all_item_ids_array[i], all_item_ids_array[j], sim))

            if (i + 1) % 1000 == 0:
                print(f"已处理 {i + 1}/{n_items} 个物品...")

        elapsed_time = time.time() - start_time
        print(f"稀疏矩阵Top-k过滤完成，耗时: {elapsed_time:.2f}秒")
        print(f"最终保留 {len(similar_pairs)} 对相似物品")

        return similar_pairs

    def build_unified_semantic_similarity_graph(self):
        """
        构建统一的物品语义相似图，包含跨域和域内的所有相似边

        返回:
        - semantic_similarity_graph: 统一的语义相似图（稀疏矩阵）
        """
        if not hasattr(self, 'similar_item_pairs') or not self.similar_item_pairs:
            print("错误：请先计算物品相似度")
            return None

        print("构建统一的物品语义相似图...")

        # 创建统一的物品图：源域物品 [0, source_item_num) + 目标域物品 [source_item_num, source_item_num + target_item_num)
        print("构建统一的物品语义相似图...")
        start_time = time.time()

        total_items = self.source_item_num + self.target_item_num

        # 收集所有边的信息，使用列表存储以便批量处理
        rows = []
        cols = []
        data = []

        # 收集所有相似边对，统一处理
        all_similarity_pairs = []

        # 1. 跨域相似边
        for sid, tid, sim in self.similar_item_pairs:
            if sid < self.source_item_num and tid < self.target_item_num:
                # 目标域物品索引需要偏移
                target_idx = self.source_item_num + tid
                all_similarity_pairs.append((sid, target_idx, sim, "跨域"))

        # 2. 源域内相似边
        if hasattr(self, 'source_similar_pairs') and self.source_similar_pairs:
            for item1_id, item2_id, sim in self.source_similar_pairs:
                if item1_id < self.source_item_num and item2_id < self.source_item_num:
                    all_similarity_pairs.append((item1_id, item2_id, sim, "源域内"))

        # 3. 目标域内相似边
        if hasattr(self, 'target_similar_pairs') and self.target_similar_pairs:
            for item1_id, item2_id, sim in self.target_similar_pairs:
                if item1_id < self.target_item_num and item2_id < self.target_item_num:
                    # 目标域物品索引需要偏移
                    item1_idx = self.source_item_num + item1_id
                    item2_idx = self.source_item_num + item2_id
                    all_similarity_pairs.append((item1_idx, item2_idx, sim, "目标域内"))

        # 统一处理所有相似边（单向边）
        edge_counts = {"跨域": 0, "源域内": 0, "目标域内": 0}
        for idx1, idx2, sim, edge_type in all_similarity_pairs:
            # 只添加单向边（较小索引指向较大索引）
            if idx1 < idx2:
                rows.append(idx1)
                cols.append(idx2)
                data.append(sim)
            else:
                rows.append(idx2)
                cols.append(idx1)
                data.append(sim)
            edge_counts[edge_type] += 1

        print(f"添加相似边: 跨域 {edge_counts['跨域']}, 源域内 {edge_counts['源域内']}, 目标域内 {edge_counts['目标域内']}")

        # 批量构建稀疏矩阵
        print("批量构建稀疏矩阵...")
        if len(rows) > 0:
            semantic_graph = sp.coo_matrix((data, (rows, cols)),
                                         shape=(total_items, total_items),
                                         dtype=np.float32)
            # 转换为CSR格式以提高后续访问效率
            semantic_graph = semantic_graph.tocsr()
        else:
            semantic_graph = sp.csr_matrix((total_items, total_items), dtype=np.float32)

        print(f"语义相似图构建耗时: {time.time() - start_time:.2f}秒，共{len(data)}条边")

        # 保存结果
        self.semantic_similarity_graph = semantic_graph

        print(f"物品语义相似图构建完成:")
        print(f"  - 总节点数: {total_items} (源域: {self.source_item_num}, 目标域: {self.target_item_num})")
        print(f"  - 总边数: {semantic_graph.nnz} 条")
        print(f"  - 跨域相似边: {len(self.similar_item_pairs) * 2} 条")
        if hasattr(self, 'source_similar_pairs'):
            print(f"  - 源域内相似边: {len(self.source_similar_pairs) * 2} 条")
        if hasattr(self, 'target_similar_pairs'):
            print(f"  - 目标域内相似边: {len(self.target_similar_pairs) * 2} 条")

        return semantic_graph

    def create_word2vec_item_graph_with_similarity(self, source_kg_path, target_kg_path,
                                                   source_entity_dict_path, target_entity_dict_path,
                                                   similarity_threshold=0.5, top_k=None,
                                                   save_embeddings=True):
        """
        创建Word2Vec物品图，统一处理所有相似度

        参数:
        - source_kg_path: 源域知识图谱路径
        - target_kg_path: 目标域知识图谱路径
        - source_entity_dict_path: 源域实体字典路径
        - target_entity_dict_path: 目标域实体字典路径
        - similarity_threshold: 相似度阈值，默认0.5
        - top_k: 每个物品保留的最相似物品数量，None表示不限制
        - save_embeddings: 是否保存嵌入到文件，默认True

        返回:
        - semantic_similarity_graph: 统一的语义相似图
        - similar_pairs: 所有相似物品对列表
        - source_embeddings: 源域物品嵌入
        - target_embeddings: 目标域物品嵌入
        """
        # 尝试加载已保存的嵌入
        if save_embeddings and self.load_word2vec_embeddings():
            print("使用已保存的Word2Vec嵌入")
        else:
            print("训练新的Word2Vec嵌入")
            self.load_kg_data(source_kg_path, target_kg_path)
            self.load_entity_dictionaries(source_entity_dict_path, target_entity_dict_path)
            self.build_item_entity_corpus()
            self.train_word2vec_embeddings()

        # 计算相似度（统一处理）
        similar_pairs = self.calculate_item_similarity(
            threshold=similarity_threshold,
            top_k=top_k
        )

        # 构建统一的语义相似图
        semantic_graph = self.build_unified_semantic_similarity_graph()

        # 保存嵌入（如果需要）
        if save_embeddings:
            print("保存Word2Vec嵌入...")
            self.save_word2vec_embeddings()

        print(f"Word2Vec语义知识图谱创建完成:")
        print(f"  - 相似物品对总数: {len(similar_pairs)}")

        return (semantic_graph, similar_pairs,
                self.source_embeddings, self.target_embeddings)
