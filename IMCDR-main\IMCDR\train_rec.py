import os
import sys
from datetime import datetime
import time
import numpy as np
import random
import argparse
import torch
from tqdm import tqdm
from model.trainer import CrossTrainer
from utils.loader import DataLoader
from utils.GraphMaker import GraphMaker
from utils import torch_utils, helper
from utils.node2vecKGBuilder import node2vecKGBuilder
from utils.embedding_loader import load_pretrained_embeddings
from utils.Word2vecKGBuilder import Word2vecKGBuilder
from utils.performance_analyzer import performance_analyzer, print_performance_summary, reset_performance_stats

parser = argparse.ArgumentParser()
# dataset part
parser.add_argument('--dataset', type=str, default='Movie_Music', help='数据集名称')

# model part
parser.add_argument('--model', type=str, default="CDRIB", help="模型名称")
parser.add_argument('--feature_dim', type=int, default=128, help='嵌入维度')
parser.add_argument('--optim', choices=['sgd', 'adagrad', 'adam', 'adamax'], default='adam',
                    help='优化器类型')
parser.add_argument('--lr', type=float, default=0.001, help='学习率')
parser.add_argument('--lr_decay', type=float, default=0.9, help='学习率衰减率')
parser.add_argument('--weight_decay', type=float, default=5e-4, help='权重衰减(L2正则化)')
parser.add_argument('--decay_epoch', type=int, default=10, help='学习率衰减的epoch')
parser.add_argument('--cpu', action='store_true', help='强制使用CPU')
parser.add_argument('--cuda', type=bool, default=torch.cuda.is_available())
parser.add_argument('--item_temperature', type=float, default=0.1, help='物品对比学习的温度系数')
parser.add_argument('--struc_similarity_threshold', type=float, default=0.9, help='结构相似度阈值')
parser.add_argument('--seman_similarity_threshold', type=float, default=0.9, help='语义相似度阈值')

# 对比学习参数
parser.add_argument('--force_regenerate', action='store_true', help='是否强制重新生成嵌入')
parser.add_argument('--item_contrastive_weight', type=float, default=1.0, help='物品对比学习损失的权重系数')
parser.add_argument('--gpu_batch_size', type=int, default=1000, help='GPU计算相似度时的批处理大小')

# LightGCN参数
parser.add_argument('--lightgcn_layers', type=int, default=3, help='LightGCN的层数')
parser.add_argument('--lightgcn_dropout', type=float, default=0.1, help='LightGCN的dropout率')
parser.add_argument('--lightgcn_reg', type=float, default=1e-4, help='LightGCN的L2正则化权重')
parser.add_argument('--lightgcn_weight', type=float, default=1.0, help='LightGCN损失的权重系数')

# 跨模态对比学习参数
parser.add_argument('--cross_modal_temperature', type=float, default=0.1, help='跨模态对比学习温度参数')
parser.add_argument('--cross_modal_weight', type=float, default=1.0, help='跨模态对比学习损失权重')

# 重叠用户对比学习参数
parser.add_argument('--overlap_user_temperature', type=float, default=0.1, help='重叠用户对比学习温度参数')
parser.add_argument('--user_CL_weight', type=float, default=1.0, help='重叠用户对比学习损失的权重系数')



# 训练参数
parser.add_argument('--num_epoch', type=int, default=100, help='训练轮数')
parser.add_argument('--batch_size', type=int, default=128, help='训练批次大小')
parser.add_argument('--save_dir', type=str, default='./saved_models', help='模型保存目录')
parser.add_argument('--id', type=str, default='00', help='模型ID')
parser.add_argument('--seed', type=int, default=2040, help='随机种子')
parser.add_argument('--load', dest='load', action='store_true', default=False, help='加载预训练模型')
parser.add_argument('--model_file', type=str, help='预训练模型文件名')

# 日志和保存参数
parser.add_argument('--log_step', type=int, default=200, help='每k步打印一次日志')
parser.add_argument('--log', type=str, default='logs.txt', help='训练日志文件')
parser.add_argument('--save_epoch', type=int, default=100, help='每k轮保存一次模型')

# 评估参数
parser.add_argument('--test_sample_number', type=int, default=99, help='测试时的负样本数量')
parser.add_argument('--similarity_top_k', type=int, default=1, help='每个物品保留的最相似物品数量，0表示不限制')

def seed_everything(seed=1111):
    random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


args = parser.parse_args()
if args.cpu:
    args.cuda = False
elif args.cuda:
    torch.cuda.manual_seed(args.seed)
init_time = time.time()
# make opt
opt = vars(args)
seed_everything(opt["seed"])


# load data adj-matrix; Now sparse tensor ,But not setting in gpu.
filename  = opt["dataset"]
source_domain = filename.split("_")[0]
target_domain = filename.split("_")[1]
source_graph = "./dataset_newest/" + filename + f"/{source_domain}_train.csv"
source_kg = "./dataset_newest/" + filename + f"/{source_domain}_kg.txt"
source_entity_dict = "./dataset_newest/" + filename + "/entity2id.csv"
source_G = GraphMaker(opt, source_graph)
source_UV = source_G.UV
source_VU = source_G.VU
source_adj = source_G.adj

target_graph = "./dataset_newest/" + filename + f"/{target_domain}_train.csv"
target_kg = "./dataset_newest/" + filename + f"/{target_domain}_kg.txt"
target_entity_dict = "./dataset_newest/" + filename + "/entity2id.csv"
target_G = GraphMaker(opt, target_graph)
target_UV = target_G.UV
target_VU = target_G.VU
target_adj = target_G.adj
print("graph loaded!")

if opt["cuda"]:
    source_UV = source_UV.cuda()
    source_VU = source_VU.cuda()
    source_adj = source_adj.cuda()

    target_UV = target_UV.cuda()
    target_VU = target_VU.cuda()
    target_adj = target_adj.cuda()


model_id = opt['id'] if len(opt['id']) > 1 else '0' + opt['id']
model_save_dir = opt['save_dir'] + '/' + model_id
opt['model_save_dir'] = model_save_dir
helper.ensure_dir(model_save_dir, verbose=True)
# save config
helper.save_config(opt, model_save_dir + '/config.json', verbose=True)
file_logger = helper.FileLogger(model_save_dir + '/' + opt['log'],
                                header="# epoch\ttrain_loss\ts_test_HR10\ts_best_test_HR10\tt_test_HR10\tt_best_test_HR10")

# 设置日志文件
log_file = model_save_dir + '/full_log.txt'
# 重定向标准输出到我们的双重日志器
sys.stdout = helper.DualLogger(log_file)
# print model info
helper.print_config(opt)


print("Loading data from {} with batch size {}...".format(opt['dataset'], opt['batch_size']))
train_batch = DataLoader(opt['dataset'], opt['batch_size'], opt, evaluation = -1)
source_test_batch = DataLoader(opt['dataset'], opt["batch_size"], opt, evaluation = 1)
target_test_batch = DataLoader(opt['dataset'], opt["batch_size"], opt, evaluation = 2)

print("source_user_num_shape", opt["source_user_num"])
print("source_user_num", len(train_batch.source_user_set))
print("target_user_num_shape", opt["target_user_num"])
print("target_user_num", len(train_batch.target_user_set))
print("source_item_num", opt["source_item_num"])
print("target_item_num", opt["target_item_num"])
print("source train data : {}, target train data {}, source test data : {}, target test data : {}".format(len(train_batch.source_train_data),len(train_batch.target_train_data),len(source_test_batch.test_data),len(target_test_batch.test_data)))
print(f"overlap users",opt["overlap_users"])

# 显示Top-k设置
print("\n开始构建相似图...")
if opt["similarity_top_k"] > 0:
    print(f"使用Top-{opt['similarity_top_k']}过滤，每个物品最多保留{opt['similarity_top_k']}个最相似的物品")
else:
    print("不使用Top-k过滤，保留所有超过阈值的相似物品对")

# model
kg_merger = node2vecKGBuilder(opt)
# 创建统一的物品结构相似图
(Structural_similarity_graph, Struc_similar_item_pairs,
 Struc_source_embeddings, Struc_target_embeddings) = kg_merger.create_merged_kg_with_embeddings(
        source_kg, target_kg, source_entity_dict, target_entity_dict,
        similarity_threshold=opt["struc_similarity_threshold"],
        dimensions=opt["feature_dim"],
        top_k=opt["similarity_top_k"]
    )

word2vec = Word2vecKGBuilder(opt)
# 创建统一的物品语义相似图
(Semantic_similarity_graph, Seman_similar_item_pairs,
 Seman_source_embeddings, Seman_target_embeddings) = word2vec.create_word2vec_item_graph_with_similarity(
    source_kg, target_kg, source_entity_dict, target_entity_dict,
    similarity_threshold=opt["seman_similarity_threshold"],
    top_k=opt["similarity_top_k"],
    save_embeddings=True
)

print(f"\n=== 物品相似图构建完成 ===")
print(f"结构相似图 (Node2Vec):")
print(f"  - 总节点数: {opt['source_item_num'] + opt['target_item_num']}")
print(f"  - 总边数: {Structural_similarity_graph.nnz}")
print(f"  - 相似物品对总数: {len(Struc_similar_item_pairs)} 对")

print(f"\n语义相似图 (Word2Vec):")
print(f"  - 总节点数: {opt['source_item_num'] + opt['target_item_num']}")
print(f"  - 总边数: {Semantic_similarity_graph.nnz}")
print(f"  - 相似物品对总数: {len(Seman_similar_item_pairs)} 对")

# 创建模型
trainer = CrossTrainer(opt)  # 创建新的训练器

# 设置统一的相似图用于基于图连边的对比学习
trainer.model.set_similarity_graphs(Structural_similarity_graph, Semantic_similarity_graph)

s_dev_score_history = [0]
t_dev_score_history = [0]

current_lr = opt['lr']
global_step = 0
global_start_time = time.time()
format_str = '{}: step {}/{} (epoch {}/{}), loss = {:.6f} ({:.3f} sec/epoch), lr: {:.6f}'
max_steps = len(train_batch) * opt['num_epoch']

best_s_hit = -1
best_s_ndcg = -1
best_t_hit = -1
best_t_ndcg = -1

# 加载预训练嵌入
print("加载预训练嵌入...")
node2vec_embeddings, word2vec_embeddings = load_pretrained_embeddings(opt)

# 设置预训练嵌入到模型
trainer.model.set_pretrained_embeddings(node2vec_embeddings, word2vec_embeddings)

# start training
print("开始训练...")


for epoch in range(1, opt['num_epoch'] + 1):
    train_loss = 0
    start_time = time.time()

    # 设置当前epoch，用于控制调试信息输出
    trainer.model.set_debug_epoch(epoch)

    # 在每个epoch开始时更新预处理的投影嵌入
    # 这样可以反映投影层参数的更新，同时避免每个batch重复计算
    if epoch > 1:  # 第一个epoch已经在set_pretrained_embeddings中预处理过了
        trainer.model.update_projected_embeddings()

    # 创建训练进度条
    train_pbar = tqdm(enumerate(train_batch),
                     total=len(train_batch),
                     desc=f"Epoch {epoch}/{opt['num_epoch']} - Training",
                     ncols=120,
                     leave=False)

    # 记录各个步骤的时间
    step_times = {
        'data_loading': 0,
        'forward_pass': 0,
        'loss_computation': 0,
        'backward_pass': 0,
        'total_batch': 0
    }

    for i, batch in train_pbar:
        batch_start_time = time.time()
        global_step += 1

        # 记录内存使用情况（每10个批次记录一次）
        if i % 10 == 0:
            performance_analyzer.record_memory_usage()

        # 数据加载时间（已经在enumerate中完成）
        data_load_time = time.time() - batch_start_time
        step_times['data_loading'] += data_load_time

        # 前向传播和损失计算
        forward_start = time.time()
        performance_analyzer.start_timer('total_reconstruct_graph')
        loss = trainer.reconstruct_graph(batch, source_UV, source_VU, target_UV, target_VU, source_adj, target_adj)
        performance_analyzer.end_timer('total_reconstruct_graph')
        forward_time = time.time() - forward_start
        step_times['forward_pass'] += forward_time

        train_loss += loss

        batch_total_time = time.time() - batch_start_time
        step_times['total_batch'] += batch_total_time

        # 更新进度条显示
        train_pbar.set_postfix({
            'Loss': f'{loss:.4f}',
            'Avg_Loss': f'{train_loss/(i+1):.4f}',
            'Batch_Time': f'{batch_total_time:.3f}s',
            'LR': f'{current_lr:.6f}'
        })

    train_pbar.close()

    duration = time.time() - start_time
    train_loss = train_loss/len(train_batch)

    # 获取详细的时间统计
    time_stats = trainer.get_time_statistics()

    # 显示详细的时间统计
    print(f"\n=== Epoch {epoch} 训练时间分析 ===")
    print(f"总训练时间: {duration:.2f}秒")
    print(f"平均每批次时间: {duration/len(train_batch):.3f}秒")
    print(f"数据加载总时间: {step_times['data_loading']:.2f}秒 ({step_times['data_loading']/duration*100:.1f}%)")
    print(f"前向传播总时间: {step_times['forward_pass']:.2f}秒 ({step_times['forward_pass']/duration*100:.1f}%)")
    print(f"批次处理总时间: {step_times['total_batch']:.2f}秒")

    # 显示模型内部时间统计
    if time_stats:
        print(f"\n=== 模型内部时间分析 (基于{time_stats['total']['count']}个批次) ===")
        for step_name, stats in time_stats.items():
            if step_name != 'total':
                print(f"{step_name:12}: 总计{stats['total']:.2f}s, 平均{stats['avg']*1000:.1f}ms, 最大{stats['max']*1000:.1f}ms")

        # 显示各步骤占比
        total_model_time = time_stats['total']['total']
        print(f"\n=== 各步骤时间占比 ===")
        for step_name, stats in time_stats.items():
            if step_name != 'total':
                percentage = (stats['total'] / total_model_time) * 100
                print(f"{step_name:12}: {percentage:.1f}%")

        # 显示对比学习损失的详细时间
        print(f"\n=== 对比学习损失时间分析 ===")
        struc_time = getattr(trainer.model, '_struc_loss_time', 0)
        seman_time = getattr(trainer.model, '_seman_loss_time', 0)
        cross_modal_time = getattr(trainer.model, '_cross_modal_loss_time', 0)
        overlap_time = getattr(trainer.model, '_overlap_user_loss_time', 0)

        total_contrastive_time = struc_time + seman_time + cross_modal_time + overlap_time
        if total_contrastive_time > 0:
            print(f"结构对比学习  : {struc_time*1000:.1f}ms ({struc_time/total_contrastive_time*100:.1f}%) [结构相似对→语义空间对比]")
            print(f"语义对比学习  : {seman_time*1000:.1f}ms ({seman_time/total_contrastive_time*100:.1f}%) [语义相似对→结构空间对比]")
            print(f"跨模态对比学习: {cross_modal_time*1000:.1f}ms ({cross_modal_time/total_contrastive_time*100:.1f}%) [双图约束负采样]")
            print(f"重叠用户对比  : {overlap_time*1000:.1f}ms ({overlap_time/total_contrastive_time*100:.1f}%) [批次内负采样]")
            print(f"对比学习总计  : {total_contrastive_time*1000:.1f}ms")

    # 重置时间统计
    trainer.reset_time_statistics()

    # 显示性能分析报告（每5个epoch显示一次详细报告）
    if epoch % 5 == 0 or epoch == 1:
        print_performance_summary()
        reset_performance_stats()

    print(format_str.format(datetime.now(), global_step, max_steps, epoch, \
                                    opt['num_epoch'], train_loss, duration, current_lr))

    # 显示各种损失
    struc_loss = trainer.model.item_struc_contrastive_loss.item()
    seman_loss = trainer.model.item_seman_contrastive_loss.item()
    overlap_user_loss = trainer.model.overlap_user_loss.item()

    print(f"结构对比损失: {struc_loss:.6f} (权重: {opt['item_contrastive_weight']:.2f}) [结构相似对→语义空间]")
    print(f"语义对比损失: {seman_loss:.6f} (权重: {opt['item_contrastive_weight']:.2f}) [语义相似对→结构空间]")
    print(f"重叠用户对比损失: {overlap_user_loss:.6f} (权重: {opt['user_CL_weight']:.2f})")


    # eval model
    print("\n=== 开始评估 ===")
    eval_start_time = time.time()
    trainer.model.eval()

    # 生成嵌入的时间
    embed_start = time.time()
    trainer.evaluate_embedding(source_UV, source_VU, target_UV, target_VU, source_adj, target_adj,epoch)
    embed_time = time.time() - embed_start
    print(f"嵌入生成时间: {embed_time:.2f}秒")

    def predict(dataloder, choose, domain_name):
        MRR = 0.0
        NDCG_1 = 0.0
        NDCG_5 = 0.0
        NDCG_10 = 0.0
        HT_1 = 0.0
        HT_5 = 0.0
        HT_10 = 0.0

        valid_entity = 0.0

        # 创建评估进度条
        eval_pbar = tqdm(enumerate(dataloder),
                        total=len(dataloder),
                        desc=f"评估{domain_name}域",
                        ncols=100,
                        leave=False)

        predict_times = []

        for _, batch in eval_pbar:
            predict_start = time.time()

            if choose:
                predictions = trainer.source_predict(batch)
            else :
                predictions = trainer.target_predict(batch)

            predict_time = time.time() - predict_start
            predict_times.append(predict_time)

            for pred in predictions:
                rank = (-pred).argsort().argsort()[0].item()

                valid_entity += 1
                MRR += 1 / (rank + 1)
                if rank < 1:
                    NDCG_1 += 1 / np.log2(rank + 2)
                    HT_1 += 1
                if rank < 5:
                    NDCG_5 += 1 / np.log2(rank + 2)
                    HT_5 += 1
                if rank < 10:
                    NDCG_10 += 1 / np.log2(rank + 2)
                    HT_10 += 1

            # 更新进度条
            eval_pbar.set_postfix({
                'Samples': int(valid_entity),
                'Pred_Time': f'{predict_time:.3f}s'
            })

        eval_pbar.close()

        # 显示预测时间统计
        total_predict_time = sum(predict_times)
        avg_predict_time = total_predict_time / len(predict_times) if predict_times else 0
        print(f"{domain_name}域预测时间: 总计{total_predict_time:.2f}秒, 平均{avg_predict_time:.3f}秒/批次")

        s_mrr = MRR / valid_entity
        s_ndcg_5 = NDCG_5 / valid_entity
        s_ndcg_10 = NDCG_10 / valid_entity
        s_hr_1 = HT_1 / valid_entity
        s_hr_5 = HT_5 / valid_entity
        s_hr_10 = HT_10 / valid_entity

        return s_mrr, s_ndcg_5, s_ndcg_10, s_hr_1, s_hr_5, s_hr_10

    # 分别评估源域和目标域
    print("评估源域...")
    s_mrr, s_ndcg_5, s_ndcg_10, s_hr_1, s_hr_5, s_hr_10 = predict(source_test_batch, 1, "源")
    print("评估目标域...")
    t_mrr, t_ndcg_5, t_ndcg_10, t_hr_1, t_hr_5, t_hr_10 = predict(target_test_batch, 0, "目标")

    eval_total_time = time.time() - eval_start_time
    print(f"总评估时间: {eval_total_time:.2f}秒")


    print("\nsource: \t{:.6f}\t{:.4f}\t{:.4f}\t{:.6f}\t{:.4f}\t{:.4f}".format(s_mrr, s_ndcg_5, s_ndcg_10, s_hr_1, s_hr_5, s_hr_10))
    print("target: \t{:.6f}\t{:.4f}\t{:.4f}\t{:.6f}\t{:.4f}\t{:.4f}".format(t_mrr, t_ndcg_5, t_ndcg_10, t_hr_1, t_hr_5, t_hr_10))

    s_dev_score = s_hr_10
    t_dev_score = t_hr_10

    if s_dev_score > max(s_dev_score_history):
        print("source best!")
        print(
            "\nsource: \tMRR: {:.6f}\tNDCG@5: {:.4f}\tNDCG@10: {:.4f}\tHR@1: {:.6f}\tHR@5: {:.4f}\tHR@10: {:.4f}".format(
                s_mrr, s_ndcg_5, s_ndcg_10, s_hr_1, s_hr_5, s_hr_10))
    if t_dev_score > max(t_dev_score_history):
        print("target best!")
        print(
            "\ntarget: \tMRR: {:.6f}\tNDCG@5: {:.4f}\tNDCG@10: {:.4f}\tHR@1: {:.6f}\tHR@5: {:.4f}\tHR@10: {:.4f}".format(
                t_mrr, t_ndcg_5, t_ndcg_10, t_hr_1, t_hr_5, t_hr_10))


    file_logger.log(
        "{}\t{:.6f}\t{:.4f}\t{:.4f}\t{:.4f}\t{:.4f}".format(epoch, train_loss, s_dev_score, max([s_dev_score] + s_dev_score_history), t_dev_score, max([t_dev_score] + t_dev_score_history)))

    print(
        "epoch {}: train_loss = {:.6f}, source_hit = {:.4f}, source_ndcg = {:.4f}, target_hit = {:.4f}, target_ndcg = {:.4f}".format(
            epoch, \
            train_loss, s_hr_10, s_ndcg_10, t_hr_10, t_ndcg_10))


    # save
    model_file = model_save_dir + '/checkpoint_epoch_{}.pt'.format(epoch)

    # 保存最佳模型
    if epoch == 1 or s_dev_score > max(s_dev_score_history):
        # copyfile(model_file, model_save_dir + '/best_model.pt')
        print("new best model saved.")


    # lr schedule
    if len(s_dev_score_history) > opt['decay_epoch'] and s_dev_score <= s_dev_score_history[-1] and \
            opt['optim'] in ['sgd', 'adagrad', 'adadelta', 'adam']:
        current_lr *= opt['lr_decay']
        trainer.update_lr(current_lr)

    s_dev_score_history += [s_dev_score]
    t_dev_score_history += [t_dev_score]
    print("")

# 在程序结束前关闭日志文件
if isinstance(sys.stdout, helper.DualLogger):
    sys.stdout.close()
    # 恢复标准输出
    sys.stdout = sys.stdout.terminal

