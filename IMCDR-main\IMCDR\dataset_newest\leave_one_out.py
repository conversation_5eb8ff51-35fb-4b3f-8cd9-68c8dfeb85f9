import pandas as pd
import argparse

def load_data(domain1_path, domain2_path):
    df1 = pd.read_csv(domain1_path)
    df2 = pd.read_csv(domain2_path)
    return df1, df2

def filter_interactions(df1, df2, min_user_interactions=5, min_item_interactions=10):
    """
    过滤交互记录：用户少于min_user_interactions、物品少于min_item_interactions的记录

    Args:
        df1: 域1的数据框
        df2: 域2的数据框
        min_user_interactions: 用户最少交互次数
        min_item_interactions: 物品最少交互次数

    Returns:
        过滤后的df1, df2
    """
    print(f"过滤前 - 域1: {len(df1)} 条记录, 域2: {len(df2)} 条记录")

    # 迭代过滤，直到满足条件
    iteration = 0
    while True:
        iteration += 1
        print(f"\n=== 过滤迭代 {iteration} ===")

        # 记录过滤前的数据量
        prev_len1, prev_len2 = len(df1), len(df2)

        # 1. 过滤用户交互次数少于阈值的记录
        user_counts1 = df1['user_id'].value_counts()
        user_counts2 = df2['user_id'].value_counts()

        valid_users1 = user_counts1[user_counts1 >= min_user_interactions].index
        valid_users2 = user_counts2[user_counts2 >= min_user_interactions].index

        df1 = df1[df1['user_id'].isin(valid_users1)]
        df2 = df2[df2['user_id'].isin(valid_users2)]

        print(f"用户过滤后 - 域1: {len(df1)} 条记录 (过滤掉 {prev_len1 - len(df1)} 条)")
        print(f"用户过滤后 - 域2: {len(df2)} 条记录 (过滤掉 {prev_len2 - len(df2)} 条)")

        # 2. 过滤物品交互次数少于阈值的记录
        item_counts1 = df1['item_id'].value_counts()
        item_counts2 = df2['item_id'].value_counts()

        valid_items1 = item_counts1[item_counts1 >= min_item_interactions].index
        valid_items2 = item_counts2[item_counts2 >= min_item_interactions].index

        prev_len1, prev_len2 = len(df1), len(df2)
        df1 = df1[df1['item_id'].isin(valid_items1)]
        df2 = df2[df2['item_id'].isin(valid_items2)]

        print(f"物品过滤后 - 域1: {len(df1)} 条记录 (过滤掉 {prev_len1 - len(df1)} 条)")
        print(f"物品过滤后 - 域2: {len(df2)} 条记录 (过滤掉 {prev_len2 - len(df2)} 条)")

        # 检查是否还有变化
        if len(df1) == prev_len1 and len(df2) == prev_len2:
            print(f"过滤收敛，迭代 {iteration} 次完成")
            break

        # 防止无限循环
        if iteration > 10:
            print("警告：过滤迭代超过10次，强制停止")
            break

    # 输出最终统计信息
    print(f"\n=== 过滤完成统计 ===")
    print(f"域1 - 用户数: {df1['user_id'].nunique()}, 物品数: {df1['item_id'].nunique()}, 交互数: {len(df1)}")
    print(f"域2 - 用户数: {df2['user_id'].nunique()}, 物品数: {df2['item_id'].nunique()}, 交互数: {len(df2)}")

    # 检查重叠用户
    overlap_users = set(df1['user_id']) & set(df2['user_id'])
    print(f"重叠用户数: {len(overlap_users)}")

    return df1, df2

def validate_filtered_data(df1, df2, min_user_interactions=5, min_item_interactions=10):
    """
    验证过滤后的数据是否满足要求
    """
    print("\n=== 数据验证 ===")

    # 检查用户交互次数
    user_counts1 = df1['user_id'].value_counts()
    user_counts2 = df2['user_id'].value_counts()

    min_user_interactions1 = user_counts1.min()
    min_user_interactions2 = user_counts2.min()

    print(f"域1最少用户交互次数: {min_user_interactions1}")
    print(f"域2最少用户交互次数: {min_user_interactions2}")

    # 检查物品交互次数
    item_counts1 = df1['item_id'].value_counts()
    item_counts2 = df2['item_id'].value_counts()

    min_item_interactions1 = item_counts1.min()
    min_item_interactions2 = item_counts2.min()

    print(f"域1最少物品交互次数: {min_item_interactions1}")
    print(f"域2最少物品交互次数: {min_item_interactions2}")

    # 验证是否满足要求
    user_valid = min_user_interactions1 >= min_user_interactions and min_user_interactions2 >= min_user_interactions
    item_valid = min_item_interactions1 >= min_item_interactions and min_item_interactions2 >= min_item_interactions

    if user_valid and item_valid:
        print("✓ 数据验证通过：所有用户和物品都满足最小交互次数要求")
    else:
        print("✗ 数据验证失败：")
        if not user_valid:
            print(f"  用户交互次数不满足要求（要求≥{min_user_interactions}）")
        if not item_valid:
            print(f"  物品交互次数不满足要求（要求≥{min_item_interactions}）")

    return user_valid and item_valid

def map_ids(df1, df2):
    # 用户ID映射，重叠用户一致
    users1 = set(df1['user_id'])
    users2 = set(df2['user_id'])
    all_users = sorted(users1 | users2)
    user2id = {u: i for i, u in enumerate(all_users)}

    # 物品ID分别映射
    items1 = sorted(set(df1['item_id']))
    items2 = sorted(set(df2['item_id']))
    item2id_1 = {item: i for i, item in enumerate(items1)}
    item2id_2 = {item: i for i, item in enumerate(items2)}

    # 应用映射
    df1_mapped = df1.copy()
    df2_mapped = df2.copy()
    df1_mapped['user_id'] = df1['user_id'].map(user2id)
    df2_mapped['user_id'] = df2['user_id'].map(user2id)
    df1_mapped['item_id'] = df1['item_id'].map(item2id_1)
    df2_mapped['item_id'] = df2['item_id'].map(item2id_2)

    return df1_mapped, df2_mapped, user2id, item2id_1, item2id_2

def leave_one_out_split(df):
    """
    留一法划分：每个用户随机选择一个交互作为测试集，其余作为训练集
    """
    train_list, test_list = [], []
    for _, group in df.groupby('user_id'):  # 使用_忽略uid变量
        group = group.sample(frac=1, random_state=42)  # 打乱
        test = group.iloc[0:1]  # 取第一个作为测试
        train = group.iloc[1:]  # 其余作为训练
        test_list.append(test)
        if not train.empty:
            train_list.append(train)

    train_df = pd.concat(train_list) if train_list else pd.DataFrame(columns=df.columns)
    test_df = pd.concat(test_list)

    print(f"留一法划分完成 - 训练集: {len(train_df)} 条, 测试集: {len(test_df)} 条")
    return train_df, test_df

def save_results(train1, test1, train2, test2, user2id, item2id_1, item2id_2):
    # 只保留user_id和item_id
    train1[['user_id', 'item_id']].to_csv('Movie_train.csv', index=False)
    test1[['user_id', 'item_id']].to_csv('Movie_test.csv', index=False)
    train2[['user_id', 'item_id']].to_csv('Book_train.csv', index=False)
    test2[['user_id', 'item_id']].to_csv('Book_test.csv', index=False)
    pd.DataFrame(list(user2id.items()), columns=['raw_user_id', 'mapped_user_id']).to_csv('user2id.csv', index=False)
    pd.DataFrame(list(item2id_1.items()), columns=['raw_item_id', 'mapped_item_id']).to_csv('item2id_domain1.csv', index=False)
    pd.DataFrame(list(item2id_2.items()), columns=['raw_item_id', 'mapped_item_id']).to_csv('item2id_domain2.csv', index=False)

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='数据集过滤和留一法划分')
    parser.add_argument('--domain1_path', type=str, default='raw_data/Book/rating.csv',
                       help='域1数据文件路径')
    parser.add_argument('--domain2_path', type=str, default='raw_data/Music/rating.csv',
                       help='域2数据文件路径')
    parser.add_argument('--min_user_interactions', type=int, default=5,
                       help='用户最少交互次数')
    parser.add_argument('--min_item_interactions', type=int, default=10,
                       help='物品最少交互次数')
    parser.add_argument('--validate', action='store_true',
                       help='是否进行数据验证')

    args = parser.parse_args()

    # 1. 加载原始数据
    print("=== 加载原始数据 ===")
    df1, df2 = load_data(args.domain1_path, args.domain2_path)
    print(f"原始数据 - 域1: {len(df1)} 条记录, 域2: {len(df2)} 条记录")

    # 2. 过滤交互记录
    print(f"\n=== 开始过滤交互记录 (用户≥{args.min_user_interactions}, 物品≥{args.min_item_interactions}) ===")
    df1_filtered, df2_filtered = filter_interactions(
        df1, df2,
        min_user_interactions=args.min_user_interactions,
        min_item_interactions=args.min_item_interactions
    )

    # 3. 数据验证（可选）
    if args.validate:
        validate_filtered_data(
            df1_filtered, df2_filtered,
            min_user_interactions=args.min_user_interactions,
            min_item_interactions=args.min_item_interactions
        )

    # 4. ID映射
    print("\n=== 开始ID映射 ===")
    df1_mapped, df2_mapped, user2id, item2id_1, item2id_2 = map_ids(df1_filtered, df2_filtered)

    # 5. 留一法划分
    print("\n=== 开始留一法划分 ===")
    train1, test1 = leave_one_out_split(df1_mapped)
    train2, test2 = leave_one_out_split(df2_mapped)

    # 6. 保存结果
    print("\n=== 保存结果 ===")
    save_results(train1, test1, train2, test2, user2id, item2id_1, item2id_2)

    # 7. 输出最终统计
    print("\n=== 最终统计信息 ===")
    print(f"训练集 - 域1: {len(train1)} 条记录, 域2: {len(train2)} 条记录")
    print(f"测试集 - 域1: {len(test1)} 条记录, 域2: {len(test2)} 条记录")
    overlap_users_train = len(set(train1['user_id']) & set(train2['user_id']))
    overlap_users_test = len(set(test1['user_id']) & set(test2['user_id']))
    print(f"训练集重叠用户数: {overlap_users_train}")
    print(f"测试集重叠用户数: {overlap_users_test}")
    print('数据过滤、划分与映射完成，结果已保存。')

if __name__ == '__main__':
    main()